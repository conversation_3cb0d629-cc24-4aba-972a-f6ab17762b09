<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.member.mapper.DiscountLadderMapper">

    <resultMap type="org.dromara.member.domain.vo.DiscountLadderVo" id="DiscountLadderResult">
        <result property="id"    column="id"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="minAmount"    column="min_amount"    />
        <result property="maxAmount"    column="max_amount"    />
        <result property="discountValue"    column="discount_value"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="createTime"    column="create_time"    />
        <result property="tenantId"    column="tenant_id"    />
    </resultMap>

</mapper>
