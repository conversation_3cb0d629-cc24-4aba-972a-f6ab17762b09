package org.dromara.member.domain.dto;

import lombok.Data;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 优惠使用请求对象
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class DiscountUseRequest {

    /**
     * 会员ID
     */
    @NotNull(message = "会员ID不能为空")
    private Long memberId;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    /**
     * 原始金额
     */
    @NotNull(message = "原始金额不能为空")
    @DecimalMin(value = "0.01", message = "原始金额必须大于0")
    private BigDecimal originalAmount;

    /**
     * 使用的规则ID列表
     */
    @NotEmpty(message = "使用的规则ID列表不能为空")
    private List<Long> ruleIds;

    /**
     * 商品信息列表
     */
    private List<DiscountRequest.ProductInfo> products;

    /**
     * 备注
     */
    private String remark;

}
