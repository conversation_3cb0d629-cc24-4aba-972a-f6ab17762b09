-- 优惠体系功能演示脚本

-- 1. 清理测试数据
DELETE FROM discount_usage_record WHERE order_no LIKE 'DEMO_%';
DELETE FROM member_discount_stats WHERE member_id = 999;
DELETE FROM discount_ladder WHERE rule_id IN (SELECT id FROM discount_rule WHERE rule_name LIKE 'DEMO_%');
DELETE FROM discount_rule WHERE rule_name LIKE 'DEMO_%';

-- 2. 插入演示优惠规则

-- 满减优惠：满100减20
INSERT INTO discount_rule (
    rule_name, rule_type, min_amount, max_amount, discount_value, 
    status, priority, can_stack, remark, create_time
) VALUES (
    'DEMO_满100减20', '1', 100.00, 199.99, 20.00, 
    '0', 1, '0', '演示满减优惠', NOW()
);

-- 满减优惠：满200减50
INSERT INTO discount_rule (
    rule_name, rule_type, min_amount, max_amount, discount_value, 
    status, priority, can_stack, remark, create_time
) VALUES (
    'DEMO_满200减50', '1', 200.00, 299.99, 50.00, 
    '0', 2, '0', '演示满减优惠', NOW()
);

-- 折扣优惠：全场8折，最高优惠100元
INSERT INTO discount_rule (
    rule_name, rule_type, min_amount, discount_value, max_discount_amount,
    status, priority, can_stack, remark, create_time
) VALUES (
    'DEMO_全场8折', '2', 0.01, 0.80, 100.00,
    '0', 3, '1', '演示折扣优惠，可叠加', NOW()
);

-- 新用户专享9折
INSERT INTO discount_rule (
    rule_name, rule_type, min_amount, discount_value, max_discount_amount,
    user_limit_type, user_limit_count, status, priority, can_stack, remark, create_time
) VALUES (
    'DEMO_新用户9折', '2', 0.01, 0.90, 50.00,
    '2', 1, '0', 4, '1', '演示新用户专享优惠', NOW()
);

-- 阶梯满减优惠
INSERT INTO discount_rule (
    rule_name, rule_type, min_amount, discount_value,
    status, priority, can_stack, remark, create_time
) VALUES (
    'DEMO_阶梯满减', '4', 100.00, 0.00,
    '0', 5, '0', '演示阶梯满减优惠', NOW()
);

-- 获取阶梯规则ID并插入阶梯配置
SET @ladder_rule_id = (SELECT id FROM discount_rule WHERE rule_name = 'DEMO_阶梯满减');

INSERT INTO discount_ladder (rule_id, min_amount, max_amount, discount_value, sort_order, create_time) VALUES
(@ladder_rule_id, 100.00, 199.99, 15.00, 1, NOW()),
(@ladder_rule_id, 200.00, 299.99, 40.00, 2, NOW()),
(@ladder_rule_id, 300.00, 499.99, 80.00, 3, NOW()),
(@ladder_rule_id, 500.00, NULL, 150.00, 4, NOW());

-- 3. 查询演示数据
SELECT '=== 演示优惠规则 ===' as info;
SELECT 
    id, rule_name, rule_type, min_amount, max_amount, discount_value, 
    max_discount_amount, user_limit_type, priority, can_stack, status
FROM discount_rule 
WHERE rule_name LIKE 'DEMO_%'
ORDER BY priority DESC;

SELECT '=== 阶梯配置 ===' as info;
SELECT 
    dl.rule_id, dr.rule_name, dl.min_amount, dl.max_amount, 
    dl.discount_value, dl.sort_order
FROM discount_ladder dl
JOIN discount_rule dr ON dl.rule_id = dr.id
WHERE dr.rule_name = 'DEMO_阶梯满减'
ORDER BY dl.sort_order;

-- 4. 演示场景说明
SELECT '=== 演示场景说明 ===' as info;
SELECT '场景1：消费120元' as scenario, '预期：满100减20，优惠20元，实付100元' as expected;
SELECT '场景2：消费250元' as scenario, '预期：满200减50，优惠50元，实付200元' as expected;
SELECT '场景3：消费350元（阶梯）' as scenario, '预期：阶梯满减80元，实付270元' as expected;
SELECT '场景4：消费100元（新用户）' as scenario, '预期：新用户9折，优惠10元，实付90元' as expected;
SELECT '场景5：消费500元（叠加）' as scenario, '预期：多规则叠加，选择最优方案' as expected;

-- 5. API调用示例
SELECT '=== API调用示例 ===' as info;
SELECT 'POST /member/discount/calculate' as api, 
       '{"memberId":999,"originalAmount":120.00,"isNewUser":false}' as request_body;
SELECT 'POST /member/discount/use' as api,
       '{"memberId":999,"orderNo":"DEMO_ORDER_001","originalAmount":120.00,"ruleIds":[规则ID]}' as request_body;

-- 6. 清理脚本（可选执行）
SELECT '=== 清理脚本（需要时执行） ===' as info;
SELECT 'DELETE FROM discount_usage_record WHERE order_no LIKE "DEMO_%";' as cleanup_sql;
SELECT 'DELETE FROM member_discount_stats WHERE member_id = 999;' as cleanup_sql;
SELECT 'DELETE FROM discount_ladder WHERE rule_id IN (SELECT id FROM discount_rule WHERE rule_name LIKE "DEMO_%");' as cleanup_sql;
SELECT 'DELETE FROM discount_rule WHERE rule_name LIKE "DEMO_%";' as cleanup_sql;
