package org.dromara.member.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.member.domain.RefundRuleConfig;

import java.math.BigDecimal;
import java.util.List;

/**
 * 退款规则配置服务接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IRefundRuleConfigService {

    /**
     * 分页查询退款规则配置
     */
    IPage<RefundRuleConfig> selectPage(Page<RefundRuleConfig> page, RefundRuleConfig config);

    /**
     * 查询退款规则配置列表
     */
    List<RefundRuleConfig> selectList(RefundRuleConfig config);

    /**
     * 根据ID查询退款规则配置
     */
    RefundRuleConfig selectById(Long id);

    /**
     * 新增退款规则配置
     */
    Boolean insert(RefundRuleConfig config);

    /**
     * 修改退款规则配置
     */
    Boolean update(RefundRuleConfig config);

    /**
     * 删除退款规则配置
     */
    Boolean deleteById(Long id);

    /**
     * 批量删除退款规则配置
     */
    Boolean deleteBatch(List<Long> ids);

    /**
     * 查询默认退款规则
     */
    RefundRuleConfig selectDefaultRule();

    /**
     * 根据订单金额匹配退款规则
     */
    RefundRuleConfig selectRuleByOrderAmount(BigDecimal orderAmount);

    /**
     * 修改状态
     */
    Boolean changeStatus(Long id, String status);

    /**
     * 设置默认规则
     */
    Boolean setDefaultRule(Long id);

} 