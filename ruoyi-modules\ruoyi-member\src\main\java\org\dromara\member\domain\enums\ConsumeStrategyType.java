package org.dromara.member.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消费策略类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ConsumeStrategyType {

    /**
     * 优先扣除充值金额
     */
    RECHARGE_FIRST("1", "优先扣除充值金额"),

    /**
     * 优先扣除赠送金额
     */
    BONUS_FIRST("2", "优先扣除赠送金额"),

    /**
     * 按比例扣除
     */
    RATIO("3", "按比例扣除");

    private final String code;
    private final String desc;

    /**
     * 根据代码获取枚举
     */
    public static ConsumeStrategyType getByCode(String code) {
        for (ConsumeStrategyType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 