package org.dromara.member.domain.dto;

import lombok.Data;
import jakarta.validation.constraints.*;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 退款请求DTO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class RefundRequestDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员ID
     */
    @NotNull(message = "会员ID不能为空")
    private Long memberId;

    /**
     * 原订单号
     */
    @NotBlank(message = "原订单号不能为空")
    private String originalOrderNo;

    /**
     * 退款金额
     */
    @NotNull(message = "退款金额不能为空")
    @DecimalMin(value = "0.01", message = "退款金额必须大于0")
    private BigDecimal refundAmount;

    /**
     * 退款规则ID（可选，不传则使用默认规则）
     */
    private Long ruleId;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 备注
     */
    private String remark;

} 