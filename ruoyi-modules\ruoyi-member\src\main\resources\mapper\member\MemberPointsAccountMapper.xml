<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.member.mapper.MemberPointsAccountMapper">

    <resultMap type="org.dromara.member.domain.vo.MemberPointsAccountVo" id="MemberPointsAccountResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="totalPoints"    column="total_points"    />
        <result property="availablePoints"    column="available_points"    />
        <result property="version"    column="version"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

</mapper>
