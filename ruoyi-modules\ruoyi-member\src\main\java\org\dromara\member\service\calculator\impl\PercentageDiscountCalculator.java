package org.dromara.member.service.calculator.impl;

import org.dromara.member.domain.dto.DiscountRequest;
import org.dromara.member.domain.vo.DiscountRuleVo;
import org.dromara.member.service.calculator.DiscountCalculator;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 折扣优惠计算器
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Component("percentageDiscountCalculator")
public class PercentageDiscountCalculator implements DiscountCalculator {

    @Override
    public BigDecimal calculateDiscount(DiscountRuleVo rule, DiscountRequest request) {
        if (!isApplicable(rule, request)) {
            return BigDecimal.ZERO;
        }

        BigDecimal amount = request.getOriginalAmount();
        
        // 计算折扣金额：原价 * (1 - 折扣比例)
        BigDecimal discountAmount = amount.multiply(BigDecimal.ONE.subtract(rule.getDiscountValue()))
                .setScale(2, RoundingMode.HALF_UP);
        
        // 检查最大优惠金额限制
        if (rule.getMaxDiscountAmount() != null && 
            discountAmount.compareTo(rule.getMaxDiscountAmount()) > 0) {
            discountAmount = rule.getMaxDiscountAmount();
        }
        
        return discountAmount;
    }

    @Override
    public boolean isApplicable(DiscountRuleVo rule, DiscountRequest request) {
        // 检查金额范围
        BigDecimal amount = request.getOriginalAmount();
        
        // 检查最小金额
        if (amount.compareTo(rule.getMinAmount()) < 0) {
            return false;
        }
        
        // 检查最大金额（如果设置了）
        if (rule.getMaxAmount() != null && amount.compareTo(rule.getMaxAmount()) > 0) {
            return false;
        }
        
        return true;
    }

    @Override
    public String getSupportedRuleType() {
        return "2"; // 折扣
    }

}
