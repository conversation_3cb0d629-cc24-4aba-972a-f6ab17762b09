package org.dromara.member.service;

import org.dromara.member.domain.vo.DiscountLadderVo;

import java.util.List;

/**
 * 阶梯优惠配置Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IDiscountLadderService {

    /**
     * 根据规则ID查询阶梯配置
     */
    List<DiscountLadderVo> getByRuleId(Long ruleId);

    /**
     * 保存阶梯配置
     */
    Boolean saveLadders(Long ruleId, List<DiscountLadderVo> ladders);

    /**
     * 删除规则的阶梯配置
     */
    Boolean deleteByRuleId(Long ruleId);

}
