<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.member.mapper.ConsumeStrategyConfigMapper">

    <resultMap type="org.dromara.member.domain.ConsumeStrategyConfig" id="ConsumeStrategyConfigResult">
        <result property="id"    column="id"    />
        <result property="strategyName"    column="strategy_name"    />
        <result property="strategyType"    column="strategy_type"    />
        <result property="rechargeRatio"    column="recharge_ratio"    />
        <result property="bonusRatio"    column="bonus_ratio"    />
        <result property="description"    column="description"    />
        <result property="status"    column="status"    />
        <result property="isDefault"    column="is_default"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!-- 查询默认消费策略 -->
    <select id="selectDefaultStrategy" resultMap="ConsumeStrategyConfigResult">
        SELECT * FROM consume_strategy_config
        WHERE status = '0' AND is_default = '1'
        LIMIT 1
    </select>

</mapper> 