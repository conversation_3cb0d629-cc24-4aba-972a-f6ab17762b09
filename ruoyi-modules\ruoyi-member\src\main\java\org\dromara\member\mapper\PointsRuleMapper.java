package org.dromara.member.mapper;

import org.dromara.member.domain.PointsRule;
import org.dromara.member.domain.vo.PointsRuleVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 积分规则配置Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface PointsRuleMapper extends BaseMapperPlus<PointsRule, PointsRuleVo> {

    /**
     * 查询有效的积分规则列表（明确指定字段避免create_dept字段错误）
     */
    List<PointsRuleVo> selectActiveRules(String status, java.util.Date currentTime);

}
