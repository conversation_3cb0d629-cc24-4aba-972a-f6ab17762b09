package org.dromara.member.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.member.domain.RechargeActivityConfig;
import org.dromara.member.mapper.RechargeActivityConfigMapper;
import org.dromara.member.service.IRechargeActivityService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 充值活动配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class RechargeActivityServiceImpl implements IRechargeActivityService {

    private final RechargeActivityConfigMapper rechargeActivityMapper;

    /**
     * 查询充值活动配置列表
     *
     * @return 充值活动配置列表
     */
    @Override
    public List<RechargeActivityConfig> selectRechargeActivityList() {
        try {
            return rechargeActivityMapper.selectList();
        } catch (Exception e) {
            log.error("查询充值活动配置列表失败", e);
            throw new RuntimeException("查询充值活动配置列表失败: " + e.getMessage());
        }
    }

    /**
     * 查询有效的充值活动配置
     *
     * @return 有效的充值活动配置列表
     */
    @Override
    public List<RechargeActivityConfig> selectActiveConfigs() {
        try {
            return rechargeActivityMapper.selectActiveConfigs();
        } catch (Exception e) {
            log.error("查询有效充值活动配置失败", e);
            throw new RuntimeException("查询有效充值活动配置失败: " + e.getMessage());
        }
    }

    /**
     * 根据充值金额匹配最优活动
     *
     * @param amount 充值金额
     * @return 匹配的活动配置
     */
    @Override
    public RechargeActivityConfig matchActivityByAmount(BigDecimal amount) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("充值金额必须大于0");
        }
        
        try {
            return rechargeActivityMapper.selectBestMatchActivity(amount);
        } catch (Exception e) {
            log.error("匹配充值活动失败，金额：{}", amount, e);
            throw new RuntimeException("匹配充值活动失败: " + e.getMessage());
        }
    }

    /**
     * 查询充值活动配置
     *
     * @param id 充值活动配置主键
     * @return 充值活动配置
     */
    @Override
    public RechargeActivityConfig selectRechargeActivityById(Long id) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("ID不能为空且必须大于0");
        }
        
        try {
            return rechargeActivityMapper.selectById(id);
        } catch (Exception e) {
            log.error("查询充值活动配置失败，ID：{}", id, e);
            throw new RuntimeException("查询充值活动配置失败: " + e.getMessage());
        }
    }

    /**
     * 新增充值活动配置
     *
     * @param config 充值活动配置
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertRechargeActivity(RechargeActivityConfig config) {
        validateRechargeActivity(config);
        
        try {
            config.setCreateTime(DateUtils.getNowDate());
            return rechargeActivityMapper.insert(config);
        } catch (Exception e) {
            log.error("新增充值活动配置失败", e);
            throw new RuntimeException("新增充值活动配置失败: " + e.getMessage());
        }
    }

    /**
     * 修改充值活动配置
     *
     * @param config 充值活动配置
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRechargeActivity(RechargeActivityConfig config) {
        if (config.getId() == null || config.getId() <= 0) {
            throw new IllegalArgumentException("ID不能为空且必须大于0");
        }
        
        validateRechargeActivity(config);
        
        try {
            config.setUpdateTime(DateUtils.getNowDate());
            return rechargeActivityMapper.updateById(config);
        } catch (Exception e) {
            log.error("修改充值活动配置失败，ID：{}", config.getId(), e);
            throw new RuntimeException("修改充值活动配置失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除充值活动配置
     *
     * @param ids 需要删除的充值活动配置主键集合
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRechargeActivityByIds(Long[] ids) {
        if (ids == null || ids.length == 0) {
            throw new IllegalArgumentException("删除ID列表不能为空");
        }
        
        try {
            return rechargeActivityMapper.deleteBatchIds(Arrays.asList(ids));
        } catch (Exception e) {
            log.error("批量删除充值活动配置失败，IDs：{}", Arrays.toString(ids), e);
            throw new RuntimeException("批量删除充值活动配置失败: " + e.getMessage());
        }
    }

    /**
     * 启用充值活动配置
     *
     * @param id 充值活动配置主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int enableRechargeActivity(Long id) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("ID不能为空且必须大于0");
        }
        
        try {
            RechargeActivityConfig config = new RechargeActivityConfig();
            config.setId(id);
            config.setStatus("1"); // 启用状态
            config.setUpdateTime(DateUtils.getNowDate());
            
            return rechargeActivityMapper.updateById(config);
        } catch (Exception e) {
            log.error("启用充值活动配置失败，ID：{}", id, e);
            throw new RuntimeException("启用充值活动配置失败: " + e.getMessage());
        }
    }

    /**
     * 禁用充值活动配置
     *
     * @param id 充值活动配置主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int disableRechargeActivity(Long id) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("ID不能为空且必须大于0");
        }
        
        try {
            RechargeActivityConfig config = new RechargeActivityConfig();
            config.setId(id);
            config.setStatus("0"); // 禁用状态
            config.setUpdateTime(DateUtils.getNowDate());
            
            return rechargeActivityMapper.updateById(config);
        } catch (Exception e) {
            log.error("禁用充值活动配置失败，ID：{}", id, e);
            throw new RuntimeException("禁用充值活动配置失败: " + e.getMessage());
        }
    }

    /**
     * 验证充值活动配置
     *
     * @param config 充值活动配置
     */
    private void validateRechargeActivity(RechargeActivityConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("充值活动配置不能为空");
        }
        
        if (StringUtils.isBlank(config.getActivityName())) {
            throw new IllegalArgumentException("活动名称不能为空");
        }
        
        if (config.getRechargeAmount() == null || config.getRechargeAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("充值金额必须大于0");
        }
        
        if (config.getBonusAmount() == null || config.getBonusAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("赠送金额不能为负数");
        }
        
        if (config.getStartTime() == null) {
            throw new IllegalArgumentException("开始时间不能为空");
        }
        
        if (config.getEndTime() == null) {
            throw new IllegalArgumentException("结束时间不能为空");
        }
        
        if (config.getStartTime().after(config.getEndTime())) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
        
        if (config.getSortOrder() == null || config.getSortOrder() < 0) {
            throw new IllegalArgumentException("排序值不能为空且不能为负数");
        }
    }
} 