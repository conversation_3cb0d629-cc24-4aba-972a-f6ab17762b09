package org.dromara.member.domain.dto;

import lombok.Data;
import jakarta.validation.constraints.*;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 消费请求DTO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class ConsumeRequestDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员ID
     */
    @NotNull(message = "会员ID不能为空")
    private Long memberId;

    /**
     * 消费金额
     */
    @NotNull(message = "消费金额不能为空")
    @DecimalMin(value = "0.01", message = "消费金额必须大于0")
    private BigDecimal consumeAmount;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    /**
     * 消费策略ID（可选，不传则使用默认策略）
     */
    private Long strategyId;

    /**
     * 备注
     */
    private String remark;

} 