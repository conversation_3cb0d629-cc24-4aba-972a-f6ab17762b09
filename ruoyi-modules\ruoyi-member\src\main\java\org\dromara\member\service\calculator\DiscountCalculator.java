package org.dromara.member.service.calculator;

import org.dromara.member.domain.dto.DiscountRequest;
import org.dromara.member.domain.vo.DiscountRuleVo;

import java.math.BigDecimal;

/**
 * 优惠计算器接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface DiscountCalculator {

    /**
     * 计算优惠金额
     *
     * @param rule 优惠规则
     * @param request 优惠请求
     * @return 优惠金额
     */
    BigDecimal calculateDiscount(DiscountRuleVo rule, DiscountRequest request);

    /**
     * 检查规则是否适用
     *
     * @param rule 优惠规则
     * @param request 优惠请求
     * @return 是否适用
     */
    boolean isApplicable(DiscountRuleVo rule, DiscountRequest request);

    /**
     * 获取支持的规则类型
     *
     * @return 规则类型
     */
    String getSupportedRuleType();

}
