package org.dromara.member.service.calculator.impl;

import cn.hutool.core.collection.CollUtil;
import org.dromara.member.domain.dto.DiscountRequest;
import org.dromara.member.domain.vo.DiscountLadderVo;
import org.dromara.member.domain.vo.DiscountRuleVo;
import org.dromara.member.service.IDiscountLadderService;
import org.dromara.member.service.calculator.DiscountCalculator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 阶梯优惠计算器
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Component("ladderDiscountCalculator")
public class LadderDiscountCalculator implements DiscountCalculator {

    @Autowired
    private IDiscountLadderService discountLadderService;

    @Override
    public BigDecimal calculateDiscount(DiscountRuleVo rule, DiscountRequest request) {
        if (!isApplicable(rule, request)) {
            return BigDecimal.ZERO;
        }

        BigDecimal amount = request.getOriginalAmount();

        // 获取阶梯配置
        List<DiscountLadderVo> ladders = discountLadderService.getByRuleId(rule.getId());
        if (CollUtil.isEmpty(ladders)) {
            return BigDecimal.ZERO;
        }

        // 找到匹配的阶梯
        for (DiscountLadderVo ladder : ladders) {
            if (amount.compareTo(ladder.getMinAmount()) >= 0) {
                // 检查最大金额
                if (ladder.getMaxAmount() == null || amount.compareTo(ladder.getMaxAmount()) <= 0) {
                    return ladder.getDiscountValue();
                }
            }
        }

        return BigDecimal.ZERO;
    }

    @Override
    public boolean isApplicable(DiscountRuleVo rule, DiscountRequest request) {
        // 检查基本金额范围
        BigDecimal amount = request.getOriginalAmount();

        // 检查最小金额
        if (amount.compareTo(rule.getMinAmount()) < 0) {
            return false;
        }

        // 阶梯优惠需要检查是否有匹配的阶梯
        List<DiscountLadderVo> ladders = discountLadderService.getByRuleId(rule.getId());
        if (CollUtil.isEmpty(ladders)) {
            return false;
        }

        // 检查是否有匹配的阶梯
        for (DiscountLadderVo ladder : ladders) {
            if (amount.compareTo(ladder.getMinAmount()) >= 0) {
                if (ladder.getMaxAmount() == null || amount.compareTo(ladder.getMaxAmount()) <= 0) {
                    return true;
                }
            }
        }

        return false;
    }

    @Override
    public String getSupportedRuleType() {
        return "4"; // 阶梯满减
    }

}
