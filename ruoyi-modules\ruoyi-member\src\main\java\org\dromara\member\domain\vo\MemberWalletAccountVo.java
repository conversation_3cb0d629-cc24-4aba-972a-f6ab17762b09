package org.dromara.member.domain.vo;

import org.dromara.member.domain.MemberWalletAccount;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 会员钱包账户视图对象 member_wallet_account
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MemberWalletAccount.class)
public class MemberWalletAccountVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 会员ID
     */
    @ExcelProperty(value = "会员ID")
    private Long memberId;

    /**
     * 会员名称
     */
    @ExcelProperty(value = "会员名称")
    private String memberName;

    /**
     * 总余额
     */
    @ExcelProperty(value = "总余额")
    private Long totalBalance;

    /**
     * 充值余额（用户实际付费）
     */
    @ExcelProperty(value = "充值余额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "用=户实际付费")
    private Long rechargeBalance;

    /**
     * 赠送余额（平台赠送）
     */
    @ExcelProperty(value = "赠送余额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "平=台赠送")
    private Long bonusBalance;

    /**
     * 冻结余额
     */
    @ExcelProperty(value = "冻结余额")
    private Long frozenBalance;


}
