package org.dromara.member.service;

import org.dromara.member.domain.vo.MemberPointsAccountVo;
import org.dromara.member.domain.vo.PointsRuleVo;

import java.util.List;

/**
 * 积分缓存服务接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IPointsCacheService {

    /**
     * 缓存有效积分规则
     */
    void cacheActiveRules(List<PointsRuleVo> rules);

    /**
     * 获取缓存的有效积分规则
     */
    List<PointsRuleVo> getCachedActiveRules();

    /**
     * 清除积分规则缓存
     */
    void clearRulesCache();

    /**
     * 缓存会员积分余额
     */
    void cacheMemberPointsBalance(Long memberId, Long balance);

    /**
     * 获取缓存的会员积分余额
     */
    Long getCachedMemberPointsBalance(Long memberId);

    /**
     * 清除会员积分余额缓存
     */
    void clearMemberPointsBalanceCache(Long memberId);

    /**
     * 缓存会员积分账户
     */
    void cacheMemberPointsAccount(Long memberId, MemberPointsAccountVo account);

    /**
     * 获取缓存的会员积分账户
     */
    MemberPointsAccountVo getCachedMemberPointsAccount(Long memberId);

    /**
     * 清除会员积分账户缓存
     */
    void clearMemberPointsAccountCache(Long memberId);

    /**
     * 刷新所有缓存
     */
    void refreshAllCache();
}
