package org.dromara.member.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.member.domain.MemberWalletDetail;
import org.dromara.member.domain.bo.MemberWalletDetailBo;
import org.dromara.member.domain.vo.MemberWalletDetailVo;
import org.dromara.member.mapper.MemberInfoMapper;
import org.dromara.member.mapper.MemberWalletDetailMapper;
import org.dromara.member.service.IWalletDetailService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 钱包流水服务实现类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WalletDetailServiceImpl implements IWalletDetailService {

    private final MemberWalletDetailMapper walletDetailMapper;
    private final MemberInfoMapper memberInfoMapper;

    @Override
    public TableDataInfo<MemberWalletDetailVo> queryPageList(MemberWalletDetailBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MemberWalletDetail> lqw = buildQueryWrapper(bo);
        Page<MemberWalletDetailVo> result = walletDetailMapper.selectVoPage(pageQuery.build(), lqw);
        result.getRecords().forEach(item -> {
            item.setMemberName(memberInfoMapper.selectVoById(item.getMemberId()).getNickname());
        });

        return TableDataInfo.build(result);
    }

    /**
     * 新增钱包流水记录
     *
     * @param bo 钱包流水记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MemberWalletDetailBo bo) {
        MemberWalletDetail add = MapstructUtils.convert(bo, MemberWalletDetail.class);
        validEntityBeforeSave(add);
        boolean flag = walletDetailMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改钱包流水记录
     *
     * @param bo 钱包流水记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MemberWalletDetailBo bo) {
        MemberWalletDetail update = MapstructUtils.convert(bo, MemberWalletDetail.class);
        validEntityBeforeSave(update);
        return walletDetailMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MemberWalletDetail entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除钱包流水记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return walletDetailMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<MemberWalletDetail> selectList(MemberWalletDetailBo detail) {
        LambdaQueryWrapper<MemberWalletDetail> wrapper = buildQueryWrapper(detail);
        wrapper.orderByDesc(MemberWalletDetail::getCreateTime);
        return walletDetailMapper.selectList(wrapper);
    }

    @Override
    public MemberWalletDetailVo selectById(Long id) {
        if (ObjectUtil.isNull(id)) {
            throw new ServiceException("流水ID不能为空");
        }
        MemberWalletDetailVo memberWalletDetailVo = walletDetailMapper.selectVoById(id);
        memberWalletDetailVo.setMemberName(memberInfoMapper.selectVoById(memberWalletDetailVo.getMemberId()).getNickname());
        return memberWalletDetailVo;
    }

    @Override
    public List<MemberWalletDetail> selectByOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            throw new ServiceException("订单号不能为空");
        }
        return walletDetailMapper.selectByOrderNo(orderNo);
    }

    @Override
    public List<MemberWalletDetail> selectRecentByMemberId(Long memberId, Integer limit) {
        if (ObjectUtil.isNull(memberId)) {
            throw new ServiceException("会员ID不能为空");
        }
        if (ObjectUtil.isNull(limit) || limit <= 0) {
            limit = 10; // 默认查询10条
        }
        return walletDetailMapper.selectRecentByMemberId(memberId, limit);
    }

    @Override
    public List<MemberWalletDetail> selectByMemberIdAndBusinessType(Long memberId, String businessType) {
        if (ObjectUtil.isNull(memberId)) {
            throw new ServiceException("会员ID不能为空");
        }
        if (StringUtils.isBlank(businessType)) {
            throw new ServiceException("业务类型不能为空");
        }
        return walletDetailMapper.selectByMemberIdAndBusinessType(memberId, businessType);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<MemberWalletDetail> buildQueryWrapper(MemberWalletDetailBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MemberWalletDetail> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MemberWalletDetail::getId);
        lqw.eq(bo.getMemberId() != null, MemberWalletDetail::getMemberId, bo.getMemberId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), MemberWalletDetail::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessType()), MemberWalletDetail::getBusinessType, bo.getBusinessType());
        lqw.eq(StringUtils.isNotBlank(bo.getAmountType()), MemberWalletDetail::getAmountType, bo.getAmountType());
        lqw.eq(bo.getChangeAmount() != null, MemberWalletDetail::getChangeAmount, bo.getChangeAmount());
        lqw.eq(bo.getBalanceBefore() != null, MemberWalletDetail::getBalanceBefore, bo.getBalanceBefore());
        lqw.eq(bo.getBalanceAfter() != null, MemberWalletDetail::getBalanceAfter, bo.getBalanceAfter());
        lqw.eq(bo.getRechargeBalanceBefore() != null, MemberWalletDetail::getRechargeBalanceBefore, bo.getRechargeBalanceBefore());
        lqw.eq(bo.getRechargeBalanceAfter() != null, MemberWalletDetail::getRechargeBalanceAfter, bo.getRechargeBalanceAfter());
        lqw.eq(bo.getBonusBalanceBefore() != null, MemberWalletDetail::getBonusBalanceBefore, bo.getBonusBalanceBefore());
        lqw.eq(bo.getBonusBalanceAfter() != null, MemberWalletDetail::getBonusBalanceAfter, bo.getBonusBalanceAfter());
        lqw.eq(bo.getActivityId() != null, MemberWalletDetail::getActivityId, bo.getActivityId());
        return lqw;
    }

}
