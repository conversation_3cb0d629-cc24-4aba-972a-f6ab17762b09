package org.dromara.member.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.member.domain.MemberWalletDetail;
import org.dromara.member.mapper.MemberWalletDetailMapper;
import org.dromara.member.service.IWalletDetailService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 钱包流水服务实现类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WalletDetailServiceImpl implements IWalletDetailService {

    private final MemberWalletDetailMapper walletDetailMapper;

    @Override
    public IPage<MemberWalletDetail> selectPage(Page<MemberWalletDetail> page, MemberWalletDetail detail) {
        LambdaQueryWrapper<MemberWalletDetail> wrapper = buildQueryWrapper(detail);
        wrapper.orderByDesc(MemberWalletDetail::getCreateTime);
        return walletDetailMapper.selectPage(page, wrapper);
    }

    @Override
    public List<MemberWalletDetail> selectList(MemberWalletDetail detail) {
        LambdaQueryWrapper<MemberWalletDetail> wrapper = buildQueryWrapper(detail);
        wrapper.orderByDesc(MemberWalletDetail::getCreateTime);
        return walletDetailMapper.selectList(wrapper);
    }

    @Override
    public MemberWalletDetail selectById(Long id) {
        if (ObjectUtil.isNull(id)) {
            throw new ServiceException("流水ID不能为空");
        }
        return walletDetailMapper.selectById(id);
    }

    @Override
    public List<MemberWalletDetail> selectByOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            throw new ServiceException("订单号不能为空");
        }
        return walletDetailMapper.selectByOrderNo(orderNo);
    }

    @Override
    public List<MemberWalletDetail> selectRecentByMemberId(Long memberId, Integer limit) {
        if (ObjectUtil.isNull(memberId)) {
            throw new ServiceException("会员ID不能为空");
        }
        if (ObjectUtil.isNull(limit) || limit <= 0) {
            limit = 10; // 默认查询10条
        }
        return walletDetailMapper.selectRecentByMemberId(memberId, limit);
    }

    @Override
    public List<MemberWalletDetail> selectByMemberIdAndBusinessType(Long memberId, String businessType) {
        if (ObjectUtil.isNull(memberId)) {
            throw new ServiceException("会员ID不能为空");
        }
        if (StringUtils.isBlank(businessType)) {
            throw new ServiceException("业务类型不能为空");
        }
        return walletDetailMapper.selectByMemberIdAndBusinessType(memberId, businessType);
    }

    @Override
    public List<MemberWalletDetail> selectExportList(MemberWalletDetail detail) {
        LambdaQueryWrapper<MemberWalletDetail> wrapper = buildQueryWrapper(detail);
        wrapper.orderByDesc(MemberWalletDetail::getCreateTime);
        return walletDetailMapper.selectList(wrapper);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<MemberWalletDetail> buildQueryWrapper(MemberWalletDetail detail) {
        LambdaQueryWrapper<MemberWalletDetail> wrapper = new LambdaQueryWrapper<>();
        
        if (ObjectUtil.isNotNull(detail)) {
            wrapper.eq(ObjectUtil.isNotNull(detail.getMemberId()), 
                      MemberWalletDetail::getMemberId, detail.getMemberId());
            wrapper.eq(StringUtils.isNotBlank(detail.getOrderNo()), 
                      MemberWalletDetail::getOrderNo, detail.getOrderNo());
            wrapper.eq(StringUtils.isNotBlank(detail.getBusinessType()), 
                      MemberWalletDetail::getBusinessType, detail.getBusinessType());
            wrapper.eq(StringUtils.isNotBlank(detail.getAmountType()), 
                      MemberWalletDetail::getAmountType, detail.getAmountType());
            wrapper.like(StringUtils.isNotBlank(detail.getRemark()), 
                        MemberWalletDetail::getRemark, detail.getRemark());
            wrapper.ge(ObjectUtil.isNotNull(detail.getCreateTime()), 
                      MemberWalletDetail::getCreateTime, detail.getCreateTime());
        }
        
        return wrapper;
    }

} 