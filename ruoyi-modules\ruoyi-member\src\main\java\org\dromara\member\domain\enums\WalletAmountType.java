package org.dromara.member.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 钱包金额类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum WalletAmountType {

    /**
     * 充值金额
     */
    RECHARGE("1", "充值金额"),

    /**
     * 赠送金额
     */
    BONUS("2", "赠送金额");

    private final String code;
    private final String desc;

    /**
     * 根据代码获取枚举
     */
    public static WalletAmountType getByCode(String code) {
        for (WalletAmountType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 