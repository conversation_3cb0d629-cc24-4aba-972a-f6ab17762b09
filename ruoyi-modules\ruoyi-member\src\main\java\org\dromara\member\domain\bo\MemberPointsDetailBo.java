package org.dromara.member.domain.bo;

import org.dromara.member.domain.MemberPointsDetail;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 积分明细记录业务对象 member_points_detail
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MemberPointsDetail.class, reverseConvertGenerate = false)
public class MemberPointsDetailBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会员ID
     */
    @NotNull(message = "会员ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long memberId;

    /**
     * 关联订单号
     */
    @NotBlank(message = "关联订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderNo;

    /**
     * 业务类型：1-消费获得，2-签到获得，3-活动获得，4-积分过期，5-管理员调整
     */
    @NotNull(message = "业务类型：1-消费获得，2-签到获得，3-活动获得，4-积分过期，5-管理员调整不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer businessType;

    /**
     * 积分变动数量（正数为增加，负数为减少）
     */
    private Long pointsChange;

    /**
     * 变动前积分余额
     */
    private Long pointsBefore;

    /**
     * 变动后积分余额
     */
    private Long pointsAfter;

    /**
     * 关联规则ID
     */
    @NotNull(message = "关联规则ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long ruleId;

    /**
     * 消费金额
     */
    @NotNull(message = "消费金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal consumeAmount;

    /**
     * 积分过期时间（可选）
     */
    @NotNull(message = "积分过期时间（可选）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date expireTime;

    /**
     * 备注说明
     */
    @NotBlank(message = "备注说明不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
