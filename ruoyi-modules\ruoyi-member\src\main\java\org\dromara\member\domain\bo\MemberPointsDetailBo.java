package org.dromara.member.domain.bo;

import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.member.domain.MemberPointsDetail;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;

import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 积分明细记录业务对象 member_points_detail
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MemberPointsDetail.class, reverseConvertGenerate = false)
public class MemberPointsDetailBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 会员ID
     */
    @NotNull(message = "会员ID不能为空")
    private Long memberId;

    /**
     * 关联订单号
     */
    @Size(max = 64, message = "关联订单号长度不能超过{max}个字符")
    private String orderNo;

    /**
     * 业务类型：1-消费获得，2-签到获得，3-活动获得，4-积分过期，5-管理员调整
     */
    @NotBlank(message = "业务类型不能为空")
    private String businessType;

    /**
     * 积分变动数量（正数为增加，负数为减少）
     */
    @NotNull(message = "积分变动数量不能为空")
    private Long pointsChange;

    /**
     * 变动前积分余额
     */
    @Min(value = 0, message = "变动前积分余额不能小于0")
    private Long pointsBefore;

    /**
     * 变动后积分余额
     */
    @Min(value = 0, message = "变动后积分余额不能小于0")
    private Long pointsAfter;

    /**
     * 关联规则ID
     */
    private Long ruleId;

    /**
     * 消费金额
     */
    @DecimalMin(value = "0.00", message = "消费金额不能小于0")
    private BigDecimal consumeAmount;

    /**
     * 积分过期时间（可选）
     */
    private Date expireTime;

    /**
     * 备注说明
     */
    @Size(max = 500, message = "备注说明长度不能超过{max}个字符")
    private String remark;

}
