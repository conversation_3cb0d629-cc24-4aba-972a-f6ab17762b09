package org.dromara.member.service;

import org.dromara.member.domain.dto.DiscountRequest;
import org.dromara.member.domain.dto.DiscountResult;
import org.dromara.member.domain.dto.DiscountUseRequest;
import org.dromara.member.domain.vo.DiscountUsageRecordVo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.math.BigDecimal;
import java.util.List;

/**
 * 优惠服务门面接口
 * 提供对外的优惠相关服务
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IDiscountService {

    /**
     * 计算订单优惠
     *
     * @param request 优惠计算请求
     * @return 优惠计算结果
     */
    DiscountResult calculateDiscount(DiscountRequest request);

    /**
     * 获取最优优惠方案
     *
     * @param request 优惠计算请求
     * @return 优惠方案列表
     */
    List<DiscountResult.DiscountPlan> getBestDiscountPlans(DiscountRequest request);

    /**
     * 使用优惠
     *
     * @param request 优惠使用请求
     * @return 使用结果
     */
    DiscountResult useDiscount(DiscountUseRequest request);

    /**
     * 退款处理
     *
     * @param orderNo 订单号
     * @param refundAmount 退款金额
     * @return 是否成功
     */
    Boolean processRefund(String orderNo, BigDecimal refundAmount);

    /**
     * 获取会员优惠使用历史
     *
     * @param memberId 会员ID
     * @param pageQuery 分页参数
     * @return 使用历史
     */
    TableDataInfo<DiscountUsageRecordVo> getMemberUsageHistory(Long memberId, PageQuery pageQuery);

    /**
     * 预览优惠效果
     *
     * @param request 优惠计算请求
     * @return 优惠预览结果
     */
    DiscountResult previewDiscount(DiscountRequest request);

}
