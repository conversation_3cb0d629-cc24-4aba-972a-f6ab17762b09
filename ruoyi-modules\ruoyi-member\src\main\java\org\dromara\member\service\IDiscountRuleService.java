package org.dromara.member.service;

import org.dromara.member.domain.vo.DiscountRuleVo;
import org.dromara.member.domain.bo.DiscountRuleBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 优惠规则配置Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IDiscountRuleService {

    /**
     * 查询优惠规则配置
     */
    DiscountRuleVo queryById(Long id);

    /**
     * 查询优惠规则配置列表
     */
    TableDataInfo<DiscountRuleVo> queryPageList(DiscountRuleBo bo, PageQuery pageQuery);

    /**
     * 查询优惠规则配置列表
     */
    List<DiscountRuleVo> queryList(DiscountRuleBo bo);

    /**
     * 新增优惠规则配置
     */
    Boolean insertByBo(DiscountRuleBo bo);

    /**
     * 修改优惠规则配置
     */
    Boolean updateByBo(DiscountRuleBo bo);

    /**
     * 校验并批量删除优惠规则配置信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取有效的优惠规则列表
     */
    List<DiscountRuleVo> getActiveRules();

    /**
     * 根据金额获取适用的优惠规则
     */
    List<DiscountRuleVo> getApplicableRules(BigDecimal amount, Long memberId, Boolean isNewUser);

    /**
     * 检查规则是否可用
     */
    boolean isRuleAvailable(Long ruleId, Long memberId);

    /**
     * 增加规则使用次数
     */
    Boolean incrementUsedCount(Long ruleId);

    /**
     * 减少规则使用次数（退款时使用）
     */
    Boolean decrementUsedCount(Long ruleId);

}
