package org.dromara.member.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.member.domain.MemberInfo;
import org.dromara.member.domain.MemberPointsAccount;
import org.dromara.member.domain.MemberStatistics;
import org.dromara.member.domain.MemberWalletAccount;
import org.dromara.member.domain.bo.MemberInfoBo;
import org.dromara.member.domain.vo.MemberInfoVo;
import org.dromara.member.domain.vo.WalletBalanceVo;
import org.dromara.member.mapper.MemberInfoMapper;
import org.dromara.member.mapper.MemberPointsAccountMapper;
import org.dromara.member.mapper.MemberStatisticsMapper;
import org.dromara.member.mapper.MemberWalletAccountMapper;
import org.dromara.member.service.IMemberService;
import org.dromara.member.service.IPointsRuleService;
import org.dromara.member.service.IPointsService;
import org.dromara.member.utils.MemberNoGenerator;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 会员服务实现类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MemberServiceImpl implements IMemberService {

    private final MemberInfoMapper memberInfoMapper;
    private final MemberStatisticsMapper memberStatisticsMapper;
    private final MemberWalletAccountMapper walletAccountMapper;
    private final MemberNoGenerator memberNoGenerator;
    private final IPointsRuleService pointsRuleService;
    private final IPointsService pointsService;
    private final MemberPointsAccountMapper memberPointsAccountMapper;

    @Override
    public MemberInfoVo getMemberById(Long memberId) {
        if (ObjectUtil.isNull(memberId)) {
            throw new ServiceException("会员ID不能为空");
        }
        MemberInfoVo memberInfo = memberInfoMapper.selectVoById(memberId);
        if (ObjectUtil.isNull(memberInfo)) {
            throw new ServiceException("会员不存在");
        }
        return memberInfo;
    }

    @Override
    public MemberInfo getMemberByNo(String memberNo) {
        if (ObjectUtil.isEmpty(memberNo)) {
            throw new ServiceException("会员编号不能为空");
        }
        LambdaQueryWrapper<MemberInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberInfo::getMemberNo, memberNo);
        return memberInfoMapper.selectOne(wrapper);
    }

    @Override
    public MemberInfo getMemberByPhone(String phone) {
        if (ObjectUtil.isEmpty(phone)) {
            throw new ServiceException("手机号不能为空");
        }
        LambdaQueryWrapper<MemberInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberInfo::getPhone, phone);
        return memberInfoMapper.selectOne(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createMember(MemberInfoBo bo) {
        try {
            MemberInfo memberInfo = MapstructUtils.convert(bo, MemberInfo.class);

            // 1. 校验会员信息
            validateMemberInfo(memberInfo);

            // 2. 生成会员编号
            String memberNo = memberNoGenerator.generateMemberNo();
            memberInfo.setMemberNo(memberNo);

            // 3. 设置默认状态
            if (ObjectUtil.isEmpty(memberInfo.getStatus())) {
                memberInfo.setStatus("0"); // 正常状态
            }

            // 4. 创建会员基础信息
            int memberResult = memberInfoMapper.insert(memberInfo);
            if (memberResult <= 0) {
                throw new ServiceException("创建会员失败");
            }

            // 5. 初始化会员统计记录
            MemberStatistics statistics = new MemberStatistics();
            statistics.setMemberId(memberInfo.getId());
            statistics.setTotalConsumeAmount(BigDecimal.ZERO);
            statistics.setTotalConsumeCount(0);
            statistics.setTotalRechargeAmount(BigDecimal.ZERO);
            statistics.setTotalRechargeCount(0);
            memberStatisticsMapper.insert(statistics);

            // 6. 创建积分账户
             pointsService.createMemberPointsAccount(memberInfo.getId());

            // 7. 创建钱包账户
            createWalletAccountForMember(memberInfo.getId());

            log.info("会员创建成功，会员ID：{}，会员编号：{}", memberInfo.getId(), memberNo);
            return true;

        } catch (Exception e) {
            log.error("创建会员失败", e);
            throw new ServiceException("创建会员失败：" + e.getMessage());
        }
    }

    @Override
    public Boolean updateMember(MemberInfo memberInfo) {
        if (ObjectUtil.isNull(memberInfo.getId())) {
            throw new ServiceException("会员ID不能为空");
        }

        // 验证会员是否存在
        MemberInfoVo existMember = getMemberById(memberInfo.getId());
        if (ObjectUtil.isNull(existMember)) {
            throw new ServiceException("会员不存在");
        }

        int result = memberInfoMapper.updateById(memberInfo);
        return result > 0;
    }

    @Override
    public Boolean validateMemberStatus(Long memberId) {
        MemberInfoVo memberInfo = getMemberById(memberId);
        // 状态为0表示正常
        return "0".equals(memberInfo.getStatus());
    }

    @Override
    public WalletBalanceVo getMemberWalletBalance(Long memberId) {
        // 验证会员存在
        MemberInfoVo memberInfo = getMemberById(memberId);

        // 查询钱包账户
        LambdaQueryWrapper<MemberWalletAccount> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberWalletAccount::getMemberId, memberId);
        MemberWalletAccount walletAccount = walletAccountMapper.selectOne(wrapper);

        if (ObjectUtil.isNull(walletAccount)) {
            // 如果钱包账户不存在，创建一个
            createWalletAccountForMember(memberId);
            walletAccount = walletAccountMapper.selectOne(wrapper);
        }

        // 构建返回数据
        WalletBalanceVo balanceVo = new WalletBalanceVo();
        balanceVo.setMemberId(memberId);
        balanceVo.setMemberNo(memberInfo.getMemberNo());
        balanceVo.setNickname(memberInfo.getNickname());
        balanceVo.setTotalBalance(walletAccount.getTotalBalance());
        balanceVo.setRechargeBalance(walletAccount.getRechargeBalance());
        balanceVo.setBonusBalance(walletAccount.getBonusBalance());
        balanceVo.setFrozenBalance(walletAccount.getFrozenBalance());

        // 计算可用余额
        BigDecimal availableBalance = walletAccount.getTotalBalance().subtract(walletAccount.getFrozenBalance());
        balanceVo.setAvailableBalance(availableBalance);

        return balanceVo;
    }

    @Override
    public void updateLastLoginInfo(Long memberId, String loginIp) {
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setId(memberId);
        memberInfoMapper.updateById(memberInfo);
    }

    /**
     * 分页查询会员信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 会员信息分页列表
     */
    @Override
    public TableDataInfo<MemberInfoVo> queryPageList(MemberInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MemberInfo> lqw = buildQueryWrapper(bo);
        Page<MemberInfoVo> result = memberInfoMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的会员信息列表
     *
     * @param bo 查询条件
     * @return 会员信息列表
     */
    @Override
    public List<MemberInfoVo> queryList(MemberInfoBo bo) {
        LambdaQueryWrapper<MemberInfo> lqw = buildQueryWrapper(bo);
        return memberInfoMapper.selectVoList(lqw);
    }

    /**
     * 删除会员信息
     *
     * @param ids       会员ID列表
     * @param isValid   是否进行校验
     * @return 删除结果
     */
    @Override
    @Transactional
    public Boolean deleteWithValidByIds(List<Long> ids, boolean isValid) {
        walletAccountMapper.delete(new LambdaQueryWrapper<MemberWalletAccount>().in(MemberWalletAccount::getId, ids));
        memberPointsAccountMapper.delete(new LambdaQueryWrapper<MemberPointsAccount>().in(MemberPointsAccount::getMemberId, ids));
        return memberInfoMapper.deleteByIds(ids) > 0;
    }

    private LambdaQueryWrapper<MemberInfo> buildQueryWrapper(MemberInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MemberInfo> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MemberInfo::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getMemberNo()), MemberInfo::getMemberNo, bo.getMemberNo());
        lqw.like(StringUtils.isNotBlank(bo.getNickname()), MemberInfo::getNickname, bo.getNickname());
        lqw.like(StringUtils.isNotBlank(bo.getRealName()), MemberInfo::getRealName, bo.getRealName());
        lqw.eq(StringUtils.isNotBlank(bo.getPhone()), MemberInfo::getPhone, bo.getPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getEmail()), MemberInfo::getEmail, bo.getEmail());
        lqw.eq(StringUtils.isNotBlank(bo.getAvatar()), MemberInfo::getAvatar, bo.getAvatar());
        lqw.eq(StringUtils.isNotBlank(bo.getGender()), MemberInfo::getGender, bo.getGender());
        lqw.eq(bo.getBirthday() != null, MemberInfo::getBirthday, bo.getBirthday());
        lqw.eq(StringUtils.isNotBlank(bo.getProvince()), MemberInfo::getProvince, bo.getProvince());
        lqw.eq(StringUtils.isNotBlank(bo.getCity()), MemberInfo::getCity, bo.getCity());
        lqw.eq(StringUtils.isNotBlank(bo.getDistrict()), MemberInfo::getDistrict, bo.getDistrict());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), MemberInfo::getAddress, bo.getAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getPassword()), MemberInfo::getPassword, bo.getPassword());
        lqw.eq(StringUtils.isNotBlank(bo.getSalt()), MemberInfo::getSalt, bo.getSalt());
        lqw.eq(StringUtils.isNotBlank(bo.getRegisterChannel()), MemberInfo::getRegisterChannel, bo.getRegisterChannel());
        lqw.eq(StringUtils.isNotBlank(bo.getRegisterIp()), MemberInfo::getRegisterIp, bo.getRegisterIp());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), MemberInfo::getStatus, bo.getStatus());
        return lqw;
    }
    /**
     * 校验会员信息
     */
    private void validateMemberInfo(MemberInfo memberInfo) {
        if (ObjectUtil.isEmpty(memberInfo.getPhone())) {
            throw new ServiceException("手机号不能为空");
        }

        // 检查手机号是否已存在
        MemberInfo existMember = getMemberByPhone(memberInfo.getPhone());
        if (ObjectUtil.isNotNull(existMember)) {
            throw new ServiceException("手机号已存在");
        }

        // 检查邮箱是否已存在（如果提供了邮箱）
        if (ObjectUtil.isNotEmpty(memberInfo.getEmail())) {
            LambdaQueryWrapper<MemberInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MemberInfo::getEmail, memberInfo.getEmail());
            MemberInfo emailMember = memberInfoMapper.selectOne(wrapper);
            if (ObjectUtil.isNotNull(emailMember)) {
                throw new ServiceException("邮箱已存在");
            }
        }
    }

    /**
     * 为会员创建钱包账户
     */
    private void createWalletAccountForMember(Long memberId) {
        MemberWalletAccount walletAccount = new MemberWalletAccount();
        walletAccount.setMemberId(memberId);
        walletAccount.setTotalBalance(BigDecimal.ZERO);
        walletAccount.setRechargeBalance(BigDecimal.ZERO);
        walletAccount.setBonusBalance(BigDecimal.ZERO);
        walletAccount.setFrozenBalance(BigDecimal.ZERO);
        walletAccount.setVersion(0);

        int result = walletAccountMapper.insert(walletAccount);
        if (result <= 0) {
            throw new ServiceException("创建钱包账户失败");
        }
    }

}
