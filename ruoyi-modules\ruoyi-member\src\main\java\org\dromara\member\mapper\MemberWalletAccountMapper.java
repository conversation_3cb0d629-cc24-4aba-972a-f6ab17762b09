package org.dromara.member.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.member.domain.MemberWalletAccount;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.math.BigDecimal;

/**
 * 会员钱包账户Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface MemberWalletAccountMapper extends BaseMapperPlus<MemberWalletAccount, MemberWalletAccount> {

    /**
     * 使用乐观锁更新钱包余额（充值）
     */
    int updateBalanceForRecharge(@Param("memberId") Long memberId,
                                @Param("rechargeAmount") BigDecimal rechargeAmount,
                                @Param("bonusAmount") BigDecimal bonusAmount,
                                @Param("totalAmount") BigDecimal totalAmount,
                                @Param("version") Integer version);

    /**
     * 使用乐观锁更新钱包余额（消费）
     */
    int updateBalanceForConsume(@Param("memberId") Long memberId,
                               @Param("rechargeDeduct") BigDecimal rechargeDeduct,
                               @Param("bonusDeduct") BigDecimal bonusDeduct,
                               @Param("totalDeduct") BigDecimal totalDeduct,
                               @Param("version") Integer version);

    /**
     * 使用乐观锁更新冻结余额
     */
    int updateFrozenBalance(@Param("memberId") Long memberId,
                           @Param("amount") BigDecimal amount,
                           @Param("version") Integer version);

} 