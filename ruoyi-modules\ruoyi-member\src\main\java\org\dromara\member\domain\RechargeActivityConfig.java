package org.dromara.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.member.domain.base.SimpleBaseEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 充值活动配置对象 recharge_activity_config
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("recharge_activity_config")
public class RechargeActivityConfig extends SimpleBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 充值金额
     */
    private BigDecimal rechargeAmount;

    /**
     * 赠送金额
     */
    private BigDecimal bonusAmount;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 状态：0-启用，1-禁用
     */
    private String status;

    /**
     * 排序（数字越大优先级越高）
     */
    private Integer sortOrder;

    /**
     * 备注说明
     */
    private String remark;

} 