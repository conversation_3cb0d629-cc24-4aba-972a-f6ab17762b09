package org.dromara.member.service;

import org.dromara.member.domain.vo.DiscountRuleVo;

import java.util.List;
import java.util.Map;

/**
 * 优惠缓存服务接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IDiscountCacheService {

    /**
     * 缓存有效的优惠规则
     */
    void cacheActiveRules(List<DiscountRuleVo> rules);

    /**
     * 获取缓存的有效优惠规则
     */
    List<DiscountRuleVo> getCachedActiveRules();

    /**
     * 清除规则缓存
     */
    void clearRulesCache();

    /**
     * 缓存会员使用统计
     */
    void cacheMemberUsageStats(Long memberId, Map<Long, Integer> stats);

    /**
     * 获取缓存的会员使用统计
     */
    Map<Long, Integer> getCachedMemberUsageStats(Long memberId);

    /**
     * 清除会员使用统计缓存
     */
    void clearMemberUsageStatsCache(Long memberId);

    /**
     * 缓存规则详情
     */
    void cacheRuleDetail(Long ruleId, DiscountRuleVo rule);

    /**
     * 获取缓存的规则详情
     */
    DiscountRuleVo getCachedRuleDetail(Long ruleId);

    /**
     * 清除规则详情缓存
     */
    void clearRuleDetailCache(Long ruleId);

}
