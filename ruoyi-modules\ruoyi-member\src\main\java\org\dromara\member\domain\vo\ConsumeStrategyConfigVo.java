package org.dromara.member.domain.vo;

import org.dromara.member.domain.ConsumeStrategyConfig;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 消费策略配置视图对象 consume_strategy_config
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ConsumeStrategyConfig.class)
public class ConsumeStrategyConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 策略名称
     */
    @ExcelProperty(value = "策略名称")
    private String strategyName;

    /**
     * 策略类型：1-优先扣除充值金额，2-优先扣除赠送金额，3-按比例扣除
     */
    @ExcelProperty(value = "策略类型：1-优先扣除充值金额，2-优先扣除赠送金额，3-按比例扣除")
    private String strategyType;

    /**
     * 充值金额扣除比例（0-100）
     */
    @ExcelProperty(value = "充值金额扣除比例", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=-100")
    private Long rechargeRatio;

    /**
     * 赠送金额扣除比例（0-100）
     */
    @ExcelProperty(value = "赠送金额扣除比例", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=-100")
    private Long bonusRatio;

    /**
     * 状态：0-启用，1-禁用
     */
    @ExcelProperty(value = "状态：0-启用，1-禁用")
    private String status;

    /**
     * 是否默认策略：0-否，1-是
     */
    @ExcelProperty(value = "是否默认策略：0-否，1-是")
    private String isDefault;

    /**
     * 备注说明
     */
    @ExcelProperty(value = "备注说明")
    private String remark;


}
