package org.dromara.member.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.member.domain.MemberWalletDetail;

import java.util.List;

/**
 * 钱包流水服务接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IWalletDetailService {

    /**
     * 分页查询钱包流水
     */
    IPage<MemberWalletDetail> selectPage(Page<MemberWalletDetail> page, MemberWalletDetail detail);

    /**
     * 查询钱包流水列表
     */
    List<MemberWalletDetail> selectList(MemberWalletDetail detail);

    /**
     * 根据ID查询钱包流水
     */
    MemberWalletDetail selectById(Long id);

    /**
     * 根据订单号查询流水记录
     */
    List<MemberWalletDetail> selectByOrderNo(String orderNo);

    /**
     * 根据会员ID查询最近的流水记录
     */
    List<MemberWalletDetail> selectRecentByMemberId(Long memberId, Integer limit);

    /**
     * 根据会员ID和业务类型查询流水记录
     */
    List<MemberWalletDetail> selectByMemberIdAndBusinessType(Long memberId, String businessType);

    /**
     * 导出钱包流水
     */
    List<MemberWalletDetail> selectExportList(MemberWalletDetail detail);

} 