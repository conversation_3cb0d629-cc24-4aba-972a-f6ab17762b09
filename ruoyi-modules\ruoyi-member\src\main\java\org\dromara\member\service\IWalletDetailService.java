package org.dromara.member.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.member.domain.MemberWalletDetail;
import org.dromara.member.domain.bo.MemberWalletDetailBo;
import org.dromara.member.domain.vo.MemberWalletDetailVo;

import java.util.Collection;
import java.util.List;

/**
 * 钱包流水服务接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IWalletDetailService {

    /**
     * 分页查询钱包流水
     */
    TableDataInfo<MemberWalletDetailVo> queryPageList(MemberWalletDetailBo bo, PageQuery pageQuery);

    /**
     * 新增钱包流水记录
     *
     * @param bo 钱包流水记录
     * @return 是否新增成功
     */
    Boolean insertByBo(MemberWalletDetailBo bo);

    /**
     * 修改钱包流水记录
     *
     * @param bo 钱包流水记录
     * @return 是否修改成功
     */
    Boolean updateByBo(MemberWalletDetailBo bo);

    /**
     * 校验并批量删除钱包流水记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询钱包流水列表
     */
    List<MemberWalletDetail> selectList(MemberWalletDetailBo detail);

    /**
     * 根据ID查询钱包流水
     */
    MemberWalletDetailVo selectById(Long id);

    /**
     * 根据订单号查询流水记录
     */
    List<MemberWalletDetail> selectByOrderNo(String orderNo);

    /**
     * 根据会员ID查询最近的流水记录
     */
    List<MemberWalletDetail> selectRecentByMemberId(Long memberId, Integer limit);

    /**
     * 根据会员ID和业务类型查询流水记录
     */
    List<MemberWalletDetail> selectByMemberIdAndBusinessType(Long memberId, String businessType);


}
