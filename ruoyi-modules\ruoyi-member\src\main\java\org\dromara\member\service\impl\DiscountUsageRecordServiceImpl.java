package org.dromara.member.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.member.domain.DiscountUsageRecord;
import org.dromara.member.domain.bo.DiscountUsageRecordBo;
import org.dromara.member.domain.vo.DiscountUsageRecordVo;
import org.dromara.member.mapper.DiscountUsageRecordMapper;
import org.dromara.member.service.IDiscountUsageRecordService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 优惠使用记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@RequiredArgsConstructor
@Service
public class DiscountUsageRecordServiceImpl implements IDiscountUsageRecordService {

    private final DiscountUsageRecordMapper baseMapper;

    /**
     * 查询优惠使用记录
     */
    @Override
    public DiscountUsageRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询优惠使用记录列表
     */
    @Override
    public TableDataInfo<DiscountUsageRecordVo> queryPageList(DiscountUsageRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<DiscountUsageRecord> lqw = buildQueryWrapper(bo);
        Page<DiscountUsageRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询优惠使用记录列表
     */
    @Override
    public List<DiscountUsageRecordVo> queryList(DiscountUsageRecordBo bo) {
        LambdaQueryWrapper<DiscountUsageRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<DiscountUsageRecord> buildQueryWrapper(DiscountUsageRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DiscountUsageRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getMemberId() != null, DiscountUsageRecord::getMemberId, bo.getMemberId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), DiscountUsageRecord::getOrderNo, bo.getOrderNo());
        lqw.eq(bo.getRuleId() != null, DiscountUsageRecord::getRuleId, bo.getRuleId());
        lqw.like(StringUtils.isNotBlank(bo.getRuleName()), DiscountUsageRecord::getRuleName, bo.getRuleName());
        lqw.eq(StringUtils.isNotBlank(bo.getRuleType()), DiscountUsageRecord::getRuleType, bo.getRuleType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), DiscountUsageRecord::getStatus, bo.getStatus());
        lqw.between(params.get("beginUsageTime") != null && params.get("endUsageTime") != null,
            DiscountUsageRecord::getUsageTime, params.get("beginUsageTime"), params.get("endUsageTime"));
        lqw.orderByDesc(DiscountUsageRecord::getCreateTime);
        return lqw;
    }

    /**
     * 新增优惠使用记录
     */
    @Override
    public Boolean insertByBo(DiscountUsageRecordBo bo) {
        DiscountUsageRecord add = MapstructUtils.convert(bo, DiscountUsageRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改优惠使用记录
     */
    @Override
    public Boolean updateByBo(DiscountUsageRecordBo bo) {
        DiscountUsageRecord update = MapstructUtils.convert(bo, DiscountUsageRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(DiscountUsageRecord entity){
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除优惠使用记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 记录优惠使用
     */
    @Override
    public Boolean recordUsage(DiscountUsageRecordBo recordBo) {
        return insertByBo(recordBo);
    }

    /**
     * 获取会员优惠使用历史
     */
    @Override
    public TableDataInfo<DiscountUsageRecordVo> getMemberUsageHistory(Long memberId, PageQuery pageQuery) {
        DiscountUsageRecordBo bo = new DiscountUsageRecordBo();
        bo.setMemberId(memberId);
        return queryPageList(bo, pageQuery);
    }

    /**
     * 根据订单号查询使用记录
     */
    @Override
    public List<DiscountUsageRecordVo> getByOrderNo(String orderNo) {
        DiscountUsageRecordBo bo = new DiscountUsageRecordBo();
        bo.setOrderNo(orderNo);
        return queryList(bo);
    }

    /**
     * 更新使用记录状态为已退款
     */
    @Override
    public Boolean updateStatusToRefunded(String orderNo) {
        LambdaQueryWrapper<DiscountUsageRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(DiscountUsageRecord::getOrderNo, orderNo);
        lqw.eq(DiscountUsageRecord::getStatus, "1"); // 已使用状态
        
        DiscountUsageRecord update = new DiscountUsageRecord();
        update.setStatus("2"); // 已退款状态
        
        return baseMapper.update(update, lqw) > 0;
    }
}
