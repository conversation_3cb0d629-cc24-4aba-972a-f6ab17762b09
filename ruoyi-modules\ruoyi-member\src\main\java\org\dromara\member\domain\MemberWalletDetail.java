package org.dromara.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.member.domain.base.SimpleBaseEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 钱包流水记录对象 member_wallet_detail
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("member_wallet_detail")
public class MemberWalletDetail extends SimpleBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 关联订单号
     */
    private String orderNo;

    /**
     * 业务类型：1-充值，2-消费，3-退款，4-转账，5-活动赠送，6-管理员调整
     */
    private String businessType;

    /**
     * 金额类型：1-充值金额，2-赠送金额
     */
    private String amountType;

    /**
     * 变动金额（正数为增加，负数为减少）
     */
    private BigDecimal changeAmount;

    /**
     * 变动前余额
     */
    private BigDecimal balanceBefore;

    /**
     * 变动后余额
     */
    private BigDecimal balanceAfter;

    /**
     * 变动前充值余额
     */
    private BigDecimal rechargeBalanceBefore;

    /**
     * 变动后充值余额
     */
    private BigDecimal rechargeBalanceAfter;

    /**
     * 变动前赠送余额
     */
    private BigDecimal bonusBalanceBefore;

    /**
     * 变动后赠送余额
     */
    private BigDecimal bonusBalanceAfter;

    /**
     * 关联活动ID
     */
    private Long activityId;

    /**
     * 备注说明
     */
    private String remark;

} 