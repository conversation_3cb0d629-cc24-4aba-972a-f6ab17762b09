package org.dromara.member.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.member.domain.RechargeActivityConfig;
import org.dromara.member.mapper.RechargeActivityConfigMapper;
import org.dromara.member.service.IRechargeActivityConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 充值活动配置服务实现类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class RechargeActivityConfigServiceImpl implements IRechargeActivityConfigService {

    private final RechargeActivityConfigMapper rechargeActivityConfigMapper;

    @Override
    public IPage<RechargeActivityConfig> selectPage(Page<RechargeActivityConfig> page, RechargeActivityConfig config) {
        LambdaQueryWrapper<RechargeActivityConfig> wrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        wrapper.like(StringUtils.isNotBlank(config.getActivityName()), 
                    RechargeActivityConfig::getActivityName, config.getActivityName());
        wrapper.eq(StringUtils.isNotBlank(config.getStatus()), 
                  RechargeActivityConfig::getStatus, config.getStatus());
        wrapper.ge(ObjectUtil.isNotNull(config.getStartTime()), 
                  RechargeActivityConfig::getStartTime, config.getStartTime());
        wrapper.le(ObjectUtil.isNotNull(config.getEndTime()), 
                  RechargeActivityConfig::getEndTime, config.getEndTime());
        wrapper.orderByDesc(RechargeActivityConfig::getSortOrder);
        wrapper.orderByDesc(RechargeActivityConfig::getCreateTime);
        
        return rechargeActivityConfigMapper.selectPage(page, wrapper);
    }

    @Override
    public List<RechargeActivityConfig> selectList(RechargeActivityConfig config) {
        LambdaQueryWrapper<RechargeActivityConfig> wrapper = new LambdaQueryWrapper<>();
        
        wrapper.like(StringUtils.isNotBlank(config.getActivityName()), 
                    RechargeActivityConfig::getActivityName, config.getActivityName());
        wrapper.eq(StringUtils.isNotBlank(config.getStatus()), 
                  RechargeActivityConfig::getStatus, config.getStatus());
        wrapper.orderByDesc(RechargeActivityConfig::getSortOrder);
        wrapper.orderByDesc(RechargeActivityConfig::getCreateTime);
        
        return rechargeActivityConfigMapper.selectList(wrapper);
    }

    @Override
    public RechargeActivityConfig selectById(Long id) {
        if (ObjectUtil.isNull(id)) {
            throw new ServiceException("配置ID不能为空");
        }
        return rechargeActivityConfigMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insert(RechargeActivityConfig config) {
        // 验证配置数据
        validateConfig(config);
        
        // 设置默认值
        if (ObjectUtil.isEmpty(config.getStatus())) {
            config.setStatus("0"); // 默认启用
        }
        
        int result = rechargeActivityConfigMapper.insert(config);
        if (result > 0) {
            log.info("新增充值活动配置成功：{}", config.getActivityName());
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(RechargeActivityConfig config) {
        if (ObjectUtil.isNull(config.getId())) {
            throw new ServiceException("配置ID不能为空");
        }
        
        // 验证配置是否存在
        RechargeActivityConfig existConfig = selectById(config.getId());
        if (ObjectUtil.isNull(existConfig)) {
            throw new ServiceException("配置不存在");
        }
        
        // 验证配置数据
        validateConfig(config);
        
        int result = rechargeActivityConfigMapper.updateById(config);
        if (result > 0) {
            log.info("更新充值活动配置成功：{}", config.getActivityName());
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        if (ObjectUtil.isNull(id)) {
            throw new ServiceException("配置ID不能为空");
        }
        
        RechargeActivityConfig config = selectById(id);
        if (ObjectUtil.isNull(config)) {
            throw new ServiceException("配置不存在");
        }
        
        int result = rechargeActivityConfigMapper.deleteById(id);
        if (result > 0) {
            log.info("删除充值活动配置成功：{}", config.getActivityName());
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteBatch(List<Long> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            throw new ServiceException("配置ID列表不能为空");
        }
        
        int result = rechargeActivityConfigMapper.deleteBatchIds(ids);
        if (result > 0) {
            log.info("批量删除充值活动配置成功，数量：{}", result);
            return true;
        }
        return false;
    }

    @Override
    public List<RechargeActivityConfig> selectActiveConfigs() {
        return rechargeActivityConfigMapper.selectActiveConfigs();
    }

    @Override
    public RechargeActivityConfig selectBestMatchActivity(BigDecimal amount) {
        if (ObjectUtil.isNull(amount) || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        return rechargeActivityConfigMapper.selectBestMatchActivity(amount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeStatus(Long id, String status) {
        if (ObjectUtil.isNull(id)) {
            throw new ServiceException("配置ID不能为空");
        }
        
        if (StringUtils.isBlank(status)) {
            throw new ServiceException("状态不能为空");
        }
        
        RechargeActivityConfig config = new RechargeActivityConfig();
        config.setId(id);
        config.setStatus(status);
        
        int result = rechargeActivityConfigMapper.updateById(config);
        if (result > 0) {
            log.info("更新充值活动配置状态成功，ID：{}，状态：{}", id, status);
            return true;
        }
        return false;
    }

    /**
     * 验证配置数据
     */
    private void validateConfig(RechargeActivityConfig config) {
        if (StringUtils.isBlank(config.getActivityName())) {
            throw new ServiceException("活动名称不能为空");
        }
        
        if (ObjectUtil.isNull(config.getRechargeAmount()) || config.getRechargeAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("充值金额必须大于0");
        }
        
        if (ObjectUtil.isNull(config.getBonusAmount()) || config.getBonusAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("赠送金额不能为负数");
        }
        
        // 检查时间范围
        if (ObjectUtil.isNotNull(config.getStartTime()) && ObjectUtil.isNotNull(config.getEndTime())) {
            if (config.getStartTime().after(config.getEndTime())) {
                throw new ServiceException("开始时间不能晚于结束时间");
            }
        }
        
        // 检查活动名称是否重复
        if (isActivityNameExists(config.getActivityName(), config.getId())) {
            throw new ServiceException("活动名称已存在");
        }
    }

    /**
     * 检查活动名称是否已存在
     */
    private boolean isActivityNameExists(String activityName, Long excludeId) {
        LambdaQueryWrapper<RechargeActivityConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RechargeActivityConfig::getActivityName, activityName);
        if (ObjectUtil.isNotNull(excludeId)) {
            wrapper.ne(RechargeActivityConfig::getId, excludeId);
        }
        return rechargeActivityConfigMapper.selectCount(wrapper) > 0;
    }

} 