package org.dromara.member.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.member.domain.ConsumeStrategyConfig;

import java.util.List;

/**
 * 消费策略配置服务接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IConsumeStrategyConfigService {

    /**
     * 分页查询消费策略配置
     */
    IPage<ConsumeStrategyConfig> selectPage(Page<ConsumeStrategyConfig> page, ConsumeStrategyConfig config);

    /**
     * 查询消费策略配置列表
     */
    List<ConsumeStrategyConfig> selectList(ConsumeStrategyConfig config);

    /**
     * 根据ID查询消费策略配置
     */
    ConsumeStrategyConfig selectById(Long id);

    /**
     * 新增消费策略配置
     */
    Boolean insert(ConsumeStrategyConfig config);

    /**
     * 修改消费策略配置
     */
    Boolean update(ConsumeStrategyConfig config);

    /**
     * 删除消费策略配置
     */
    Boolean deleteById(Long id);

    /**
     * 批量删除消费策略配置
     */
    Boolean deleteBatch(List<Long> ids);

    /**
     * 查询默认消费策略
     */
    ConsumeStrategyConfig selectDefaultStrategy();

    /**
     * 修改状态
     */
    Boolean changeStatus(Long id, String status);

    /**
     * 设置默认策略
     */
    Boolean setDefaultStrategy(Long id);

} 