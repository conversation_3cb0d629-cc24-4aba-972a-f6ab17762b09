package org.dromara.member.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.member.domain.bo.MemberPointsDetailBo;
import org.dromara.member.service.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 积分异步处理服务实现类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PointsAsyncServiceImpl implements IPointsAsyncService {

    private final IPointsRuleService pointsRuleService;
    private final IMemberPointsAccountService memberPointsAccountService;
    private final IMemberPointsDetailService memberPointsDetailService;
    private final IPointsCacheService pointsCacheService;

    /**
     * 异步处理消费积分获取
     */
    @Override
    @Async("pointsTaskExecutor")
    public void asyncConsumeEarnPoints(Long memberId, BigDecimal amount, String orderNo) {
        try {
            log.info("开始异步处理消费积分获取，会员ID：{}，消费金额：{}", memberId, amount);

            // 计算积分
            Long points = pointsRuleService.calculatePoints(amount);
            if (points <= 0) {
                log.info("消费金额{}未达到积分获取条件", amount);
                return;
            }

            // 增加积分
            boolean success = memberPointsAccountService.addPoints(memberId, points, orderNo, amount);
            if (success) {
                log.info("异步处理消费积分获取成功，会员{}获得{}积分", memberId, points);
            } else {
                log.error("异步处理消费积分获取失败，会员ID：{}", memberId);
            }
        } catch (Exception e) {
            log.error("异步处理消费积分获取异常，会员ID：{}，消费金额：{}", memberId, amount, e);
        }
    }

    /**
     * 异步记录积分明细
     */
    @Override
    @Async("pointsTaskExecutor")
    public void asyncRecordPointsDetail(MemberPointsDetailBo detailBo) {
        try {
            log.debug("开始异步记录积分明细，会员ID：{}", detailBo.getMemberId());

            boolean success = memberPointsDetailService.recordPointsChange(detailBo);
            if (success) {
                log.debug("异步记录积分明细成功，会员ID：{}", detailBo.getMemberId());
            } else {
                log.error("异步记录积分明细失败，会员ID：{}", detailBo.getMemberId());
            }
        } catch (Exception e) {
            log.error("异步记录积分明细异常，会员ID：{}", detailBo.getMemberId(), e);
        }
    }

    /**
     * 异步刷新积分缓存
     */
    @Override
    @Async("pointsTaskExecutor")
    public void asyncRefreshMemberPointsCache(Long memberId) {
        try {
            log.debug("开始异步刷新会员积分缓存，会员ID：{}", memberId);

            // 清除旧缓存
            pointsCacheService.clearMemberPointsAccountCache(memberId);
            pointsCacheService.clearMemberPointsBalanceCache(memberId);

            // 重新加载缓存
            memberPointsAccountService.queryByMemberId(memberId);
            memberPointsAccountService.getMemberPoints(memberId);

            log.debug("异步刷新会员积分缓存成功，会员ID：{}", memberId);
        } catch (Exception e) {
            log.error("异步刷新会员积分缓存异常，会员ID：{}", memberId, e);
        }
    }

    /**
     * 异步刷新积分规则缓存
     */
    @Override
    @Async("pointsTaskExecutor")
    public void asyncRefreshRulesCache() {
        try {
            log.debug("开始异步刷新积分规则缓存");

            // 清除旧缓存
            pointsCacheService.clearRulesCache();

            // 重新加载缓存
            pointsRuleService.getActiveRules();

            log.debug("异步刷新积分规则缓存成功");
        } catch (Exception e) {
            log.error("异步刷新积分规则缓存异常", e);
        }
    }
}
