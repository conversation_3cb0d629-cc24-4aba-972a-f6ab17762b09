package org.dromara.member.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.member.domain.*;
import org.dromara.member.domain.dto.ConsumeRequestDto;
import org.dromara.member.domain.dto.RechargeRequestDto;
import org.dromara.member.domain.dto.RefundRequestDto;
import org.dromara.member.domain.enums.ConsumeStrategyType;
import org.dromara.member.domain.enums.WalletAmountType;
import org.dromara.member.domain.enums.WalletBusinessType;
import org.dromara.member.domain.vo.RechargeResultVo;
import org.dromara.member.domain.vo.WalletBalanceVo;
import org.dromara.member.domain.vo.WalletOperationResultVo;
import org.dromara.member.mapper.*;
import org.dromara.member.service.IMemberService;
import org.dromara.member.service.IPointsRuleService;
import org.dromara.member.service.IPointsService;
import org.dromara.member.service.IWalletService;
import org.dromara.member.utils.MemberNoGenerator;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 钱包服务实现类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WalletServiceImpl implements IWalletService {

    private final IMemberService memberService;
    private final MemberWalletAccountMapper walletAccountMapper;
    private final MemberWalletDetailMapper walletDetailMapper;
    private final MemberStatisticsMapper statisticsMapper;
    private final RechargeActivityConfigMapper activityConfigMapper;
    private final ConsumeStrategyConfigMapper strategyConfigMapper;
    private final RefundRuleConfigMapper refundRuleConfigMapper;
    private final MemberNoGenerator memberNoGenerator;
    private final IPointsRuleService pointsRuleService;
    private final IPointsService pointsService;

    @Override
    public WalletBalanceVo getWalletBalance(Long memberId) {
        return memberService.getMemberWalletBalance(memberId);
    }


    /**
     * 充值操作
     *
     * @param request 充值请求
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RechargeResultVo recharge(RechargeRequestDto request) {
        try {
            // 1. 验证会员状态
            if (!memberService.validateMemberStatus(request.getMemberId())) {
                throw new ServiceException("会员状态异常，无法充值");
            }

            // 2. 匹配充值活动规则
            RechargeActivityConfig activity = activityConfigMapper.selectBestMatchActivity(request.getRechargeAmount());
            BigDecimal bonusAmount = BigDecimal.ZERO;
            if (ObjectUtil.isNotNull(activity)) {
                bonusAmount = activity.getBonusAmount();
                log.info("匹配到充值活动：{}，赠送金额：{}", activity.getActivityName(), bonusAmount);
            }

            // 3. 计算总金额
            BigDecimal totalAmount = request.getRechargeAmount().add(bonusAmount);

            // 4. 生成充值订单号
            String orderNo = memberNoGenerator.generateOrderNo();

            // 5. 执行充值操作（带重试机制）
            boolean rechargeSuccess = executeRechargeWithRetry(request.getMemberId(),
                request.getRechargeAmount(), bonusAmount, totalAmount, orderNo, activity);

            if (!rechargeSuccess) {
                throw new ServiceException("充值失败，请稍后重试");
            }

            // 6. 构建返回结果
            RechargeResultVo result = new RechargeResultVo();
            result.setOrderNo(orderNo);
            result.setMemberId(request.getMemberId());
            result.setRechargeAmount(request.getRechargeAmount());
            result.setBonusAmount(bonusAmount);
            result.setActivityName(ObjectUtil.isNotNull(activity) ? activity.getActivityName() : null);
            result.setPaymentStatus("SUCCESS");

            // 获取最新余额
            WalletBalanceVo balance = getWalletBalance(request.getMemberId());
            result.setBalanceAfter(balance.getTotalBalance());
            result.setBalanceBefore(balance.getTotalBalance().subtract(totalAmount));

            log.info("充值成功，会员ID：{}，充值金额：{}，赠送金额：{}",
                request.getMemberId(), request.getRechargeAmount(), bonusAmount);

            return result;

        } catch (Exception e) {
            log.error("充值失败", e);
            throw new ServiceException("充值失败：" + e.getMessage());
        }
    }


    /**
     * 消费操作
     *
     * @param request 消费请求
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public WalletOperationResultVo consume(ConsumeRequestDto request) {
        try {
            // 1. 验证会员状态和余额
            if (!memberService.validateMemberStatus(request.getMemberId())) {
                throw new ServiceException("会员状态异常，无法消费");
            }

            if (!checkSufficientBalance(request.getMemberId(), request.getConsumeAmount())) {
                return buildFailureResult(request.getMemberId(), request.getOrderNo(),
                    request.getConsumeAmount(), "余额不足");
            }

            // 2. 获取消费策略
            ConsumeStrategyConfig strategy = getConsumeStrategy(request.getStrategyId());

            // 3. 计算扣款分配
            ConsumeAllocation allocation = calculateConsumeAllocation(
                request.getMemberId(), request.getConsumeAmount(), strategy);

            // 4. 执行消费操作（带重试机制）
            boolean consumeSuccess = executeConsumeWithRetry(request.getMemberId(),
                allocation, request.getOrderNo());

            if (!consumeSuccess) {
                return buildFailureResult(request.getMemberId(), request.getOrderNo(),
                    request.getConsumeAmount(), "消费失败，请稍后重试");
            }

            // 5. 计算获得积分
            Long pointsEarned = pointsRuleService.calculatePoints(request.getConsumeAmount());

            // 6. 更新积分账户
            pointsService.consumeEarnPoints(request.getMemberId(), request.getConsumeAmount(), request.getOrderNo());

            // 7. 构建成功结果
            WalletOperationResultVo result = buildSuccessResult(request.getMemberId(),
                request.getOrderNo(), request.getConsumeAmount());
            result.setRechargeAmountUsed(allocation.getRechargeAmount());
            result.setBonusAmountUsed(allocation.getBonusAmount());
            result.setPointsEarned(pointsEarned);
            result.setDescription("消费成功");

            log.info("消费成功，会员ID：{}，消费金额：{}，获得积分：{}",
                request.getMemberId(), request.getConsumeAmount(), pointsEarned);

            return result;

        } catch (Exception e) {
            log.error("消费失败", e);
            return buildFailureResult(request.getMemberId(), request.getOrderNo(),
                request.getConsumeAmount(), "消费失败：" + e.getMessage());
        }
    }

    /**
     * 退款操作
     *
     * @param request 退款请求
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public WalletOperationResultVo refund(RefundRequestDto request) {
        try {
            // 1. 验证会员状态
            if (!memberService.validateMemberStatus(request.getMemberId())) {
                throw new ServiceException("会员状态异常，无法退款");
            }

            // 2. 查询原消费记录
            List<MemberWalletDetail> originalRecords = walletDetailMapper.selectByOrderNo(request.getOriginalOrderNo());
            if (ObjectUtil.isEmpty(originalRecords)) {
                return buildFailureResult(request.getMemberId(), request.getOriginalOrderNo(),
                    request.getRefundAmount(), "原订单不存在");
            }

            // 3. 获取退款规则
            RefundRuleConfig refundRule = getRefundRule(request.getRuleId(), request.getRefundAmount());

            // 4. 计算退款分配
            RefundAllocation allocation = calculateRefundAllocation(originalRecords,
                request.getRefundAmount(), refundRule);

            // 5. 生成退款订单号
            String refundOrderNo = memberNoGenerator.generateOrderNo();

            // 6. 执行退款操作
            boolean refundSuccess = executeRefundWithRetry(request.getMemberId(),
                allocation, refundOrderNo);

            if (!refundSuccess) {
                return buildFailureResult(request.getMemberId(), refundOrderNo,
                    request.getRefundAmount(), "退款失败，请稍后重试");
            }

            // 7. 构建成功结果
            WalletOperationResultVo result = buildSuccessResult(request.getMemberId(),
                refundOrderNo, request.getRefundAmount());
            result.setRechargeAmountUsed(allocation.getRechargeAmount());
            result.setBonusAmountUsed(allocation.getBonusAmount());
            result.setDescription("退款成功");

            log.info("退款成功，会员ID：{}，退款金额：{}", request.getMemberId(), request.getRefundAmount());

            return result;

        } catch (Exception e) {
            log.error("退款失败", e);
            return buildFailureResult(request.getMemberId(), "",
                request.getRefundAmount(), "退款失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean freezeBalance(Long memberId, BigDecimal amount, String reason) {
        return executeWithRetry(() -> {
            MemberWalletAccount account = getWalletAccountWithLock(memberId);

            // 检查可用余额是否充足
            BigDecimal availableBalance = account.getTotalBalance().subtract(account.getFrozenBalance());
            if (availableBalance.compareTo(amount) < 0) {
                throw new ServiceException("可用余额不足，无法冻结");
            }

            // 更新冻结余额
            int result = walletAccountMapper.updateFrozenBalance(memberId, amount, account.getVersion());
            if (result <= 0) {
                throw new ServiceException("冻结余额失败");
            }

            // 记录流水
            recordWalletDetail(memberId, null, WalletBusinessType.ADMIN_ADJUST,
                WalletAmountType.RECHARGE, amount.negate(), account, reason);

            return true;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean unfreezeBalance(Long memberId, BigDecimal amount, String reason) {
        return executeWithRetry(() -> {
            MemberWalletAccount account = getWalletAccountWithLock(memberId);

            // 检查冻结余额是否充足
            if (account.getFrozenBalance().compareTo(amount) < 0) {
                throw new ServiceException("冻结余额不足，无法解冻");
            }

            // 更新冻结余额
            int result = walletAccountMapper.updateFrozenBalance(memberId, amount.negate(), account.getVersion());
            if (result <= 0) {
                throw new ServiceException("解冻余额失败");
            }

            // 记录流水
            recordWalletDetail(memberId, null, WalletBusinessType.ADMIN_ADJUST,
                WalletAmountType.RECHARGE, amount, account, reason);

            return true;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createWalletAccount(Long memberId) {
        // 检查是否已存在
        LambdaQueryWrapper<MemberWalletAccount> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberWalletAccount::getMemberId, memberId);
        MemberWalletAccount existAccount = walletAccountMapper.selectOne(wrapper);

        if (ObjectUtil.isNotNull(existAccount)) {
            throw new ServiceException("钱包账户已存在");
        }

        MemberWalletAccount account = new MemberWalletAccount();
        account.setMemberId(memberId);
        account.setTotalBalance(BigDecimal.ZERO);
        account.setRechargeBalance(BigDecimal.ZERO);
        account.setBonusBalance(BigDecimal.ZERO);
        account.setFrozenBalance(BigDecimal.ZERO);
        account.setVersion(0);

        int result = walletAccountMapper.insert(account);
        return result > 0;
    }

    @Override
    public Boolean checkSufficientBalance(Long memberId, BigDecimal amount) {
        MemberWalletAccount account = getWalletAccount(memberId);
        BigDecimal availableBalance = account.getTotalBalance().subtract(account.getFrozenBalance());
        return availableBalance.compareTo(amount) >= 0;
    }

    // ==================== 私有方法 ====================

    /**
     * 执行充值操作（带重试机制）
     */
    private boolean executeRechargeWithRetry(Long memberId, BigDecimal rechargeAmount,
                                             BigDecimal bonusAmount, BigDecimal totalAmount,
                                             String orderNo, RechargeActivityConfig activity) {

        MemberWalletAccount account = getWalletAccountWithLock(memberId);

        // 更新钱包余额
        int result = walletAccountMapper.updateBalanceForRecharge(memberId,
            rechargeAmount, bonusAmount, totalAmount, account.getVersion());

        if (result <= 0) {
            throw new ServiceException("更新钱包余额失败");
        }

        // 记录充值流水
        recordWalletDetail(memberId, orderNo, WalletBusinessType.RECHARGE,
            WalletAmountType.RECHARGE, rechargeAmount, account, "充值");

        // 记录赠送流水（如果有赠送）
        if (bonusAmount.compareTo(BigDecimal.ZERO) > 0) {
            recordWalletDetail(memberId, orderNo, WalletBusinessType.ACTIVITY_BONUS,
                WalletAmountType.BONUS, bonusAmount, account, "充值赠送", activity.getId());
        }

        // 更新会员统计
        updateMemberStatisticsForRecharge(memberId, rechargeAmount);

        return true;
    }

    /**
     * 执行消费操作（带重试机制）
     */
    private boolean executeConsumeWithRetry(Long memberId, ConsumeAllocation allocation, String orderNo) {

        MemberWalletAccount account = getWalletAccountWithLock(memberId);

        // 更新钱包余额
        BigDecimal totalDeduct = allocation.getRechargeAmount().add(allocation.getBonusAmount());
        int result = walletAccountMapper.updateBalanceForConsume(memberId,
            allocation.getRechargeAmount(), allocation.getBonusAmount(), totalDeduct, account.getVersion());

        if (result <= 0) {
            throw new ServiceException("更新钱包余额失败");
        }

        // 记录消费流水
        if (allocation.getRechargeAmount().compareTo(BigDecimal.ZERO) > 0) {
            recordWalletDetail(memberId, orderNo, WalletBusinessType.CONSUME,
                WalletAmountType.RECHARGE, allocation.getRechargeAmount().negate(), account, "消费");
        }

        if (allocation.getBonusAmount().compareTo(BigDecimal.ZERO) > 0) {
            recordWalletDetail(memberId, orderNo, WalletBusinessType.CONSUME,
                WalletAmountType.BONUS, allocation.getBonusAmount().negate(), account, "消费");
        }

        // 更新会员统计
        updateMemberStatisticsForConsume(memberId, totalDeduct);

        return true;
    }

    /**
     * 执行退款操作（带重试机制）
     */
    private boolean executeRefundWithRetry(Long memberId, RefundAllocation allocation, String orderNo) {

        MemberWalletAccount account = getWalletAccountWithLock(memberId);

        // 更新钱包余额（退款是增加余额）
        BigDecimal totalRefund = allocation.getRechargeAmount().add(allocation.getBonusAmount());
        int result = walletAccountMapper.updateBalanceForRecharge(memberId,
            allocation.getRechargeAmount(), allocation.getBonusAmount(), totalRefund, account.getVersion());

        if (result <= 0) {
            throw new ServiceException("更新钱包余额失败");
        }

        // 记录退款流水
        if (allocation.getRechargeAmount().compareTo(BigDecimal.ZERO) > 0) {
            recordWalletDetail(memberId, orderNo, WalletBusinessType.REFUND,
                WalletAmountType.RECHARGE, allocation.getRechargeAmount(), account, "退款");
        }

        if (allocation.getBonusAmount().compareTo(BigDecimal.ZERO) > 0) {
            recordWalletDetail(memberId, orderNo, WalletBusinessType.REFUND,
                WalletAmountType.BONUS, allocation.getBonusAmount(), account, "退款");
        }

        return true;
    }

    // 其他私有辅助方法...
    // (由于篇幅限制，其他方法将在后续补充)

    /**
     * 获取钱包账户（带锁）
     */
    private MemberWalletAccount getWalletAccountWithLock(Long memberId) {
        LambdaQueryWrapper<MemberWalletAccount> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberWalletAccount::getMemberId, memberId);
        MemberWalletAccount account = walletAccountMapper.selectOne(wrapper);

        if (ObjectUtil.isNull(account)) {
            throw new ServiceException("钱包账户不存在");
        }

        return account;
    }

    /**
     * 获取钱包账户
     */
    private MemberWalletAccount getWalletAccount(Long memberId) {
        return getWalletAccountWithLock(memberId);
    }

    /**
     * 记录钱包流水
     */
    private void recordWalletDetail(Long memberId, String orderNo, WalletBusinessType businessType,
                                    WalletAmountType amountType, BigDecimal changeAmount,
                                    MemberWalletAccount beforeAccount, String remark) {
        recordWalletDetail(memberId, orderNo, businessType, amountType, changeAmount, beforeAccount, remark, null);
    }

    /**
     * 记录钱包流水（完整版）
     */
    private void recordWalletDetail(Long memberId, String orderNo, WalletBusinessType businessType,
                                    WalletAmountType amountType, BigDecimal changeAmount,
                                    MemberWalletAccount beforeAccount, String remark, Long activityId) {

        MemberWalletDetail detail = new MemberWalletDetail();
        detail.setMemberId(memberId);
        detail.setOrderNo(orderNo);
        detail.setBusinessType(businessType.getCode());
        detail.setAmountType(amountType.getCode());
        detail.setChangeAmount(changeAmount);
        detail.setBalanceBefore(beforeAccount.getTotalBalance());
        detail.setBalanceAfter(beforeAccount.getTotalBalance().add(changeAmount));
        detail.setRechargeBalanceBefore(beforeAccount.getRechargeBalance());
        detail.setBonusBalanceBefore(beforeAccount.getBonusBalance());
        detail.setActivityId(activityId);
        detail.setRemark(remark);

        // 计算变动后的余额
        if (WalletAmountType.RECHARGE.equals(amountType)) {
            detail.setRechargeBalanceAfter(beforeAccount.getRechargeBalance().add(changeAmount));
            detail.setBonusBalanceAfter(beforeAccount.getBonusBalance());
        } else {
            detail.setRechargeBalanceAfter(beforeAccount.getRechargeBalance());
            detail.setBonusBalanceAfter(beforeAccount.getBonusBalance().add(changeAmount));
        }

        walletDetailMapper.insert(detail);
    }

    /**
     * 获取消费策略
     */
    private ConsumeStrategyConfig getConsumeStrategy(Long strategyId) {
        if (ObjectUtil.isNotNull(strategyId)) {
            ConsumeStrategyConfig strategy = strategyConfigMapper.selectById(strategyId);
            if (ObjectUtil.isNotNull(strategy) && "0".equals(strategy.getStatus())) {
                return strategy;
            }
        }

        // 如果指定策略不存在或已禁用，使用默认策略
        ConsumeStrategyConfig defaultStrategy = strategyConfigMapper.selectDefaultStrategy();
        if (ObjectUtil.isNull(defaultStrategy)) {
            throw new ServiceException("未找到可用的消费策略配置");
        }
        return defaultStrategy;
    }

    /**
     * 计算消费分配
     */
    private ConsumeAllocation calculateConsumeAllocation(Long memberId, BigDecimal amount, ConsumeStrategyConfig strategy) {
        // 获取当前钱包余额
        MemberWalletAccount account = getWalletAccount(memberId);

        BigDecimal rechargeBalance = account.getRechargeBalance();
        BigDecimal bonusBalance = account.getBonusBalance();
        BigDecimal availableBalance = rechargeBalance.add(bonusBalance);

        // 检查余额是否充足
        if (availableBalance.compareTo(amount) < 0) {
            throw new ServiceException("余额不足");
        }

        String strategyType = strategy.getStrategyType();
        BigDecimal rechargeAmount = BigDecimal.ZERO;
        BigDecimal bonusAmount = BigDecimal.ZERO;

        switch (strategyType) {
            case "RECHARGE_FIRST": // 充值金额优先
                if (rechargeBalance.compareTo(amount) >= 0) {
                    rechargeAmount = amount;
                } else {
                    rechargeAmount = rechargeBalance;
                    bonusAmount = amount.subtract(rechargeAmount);
                }
                break;

            case "BONUS_FIRST": // 赠送金额优先
                if (bonusBalance.compareTo(amount) >= 0) {
                    bonusAmount = amount;
                } else {
                    bonusAmount = bonusBalance;
                    rechargeAmount = amount.subtract(bonusAmount);
                }
                break;

            case "RATIO": // 按比例消费
                BigDecimal rechargeRatio = strategy.getRechargeRatio().divide(new BigDecimal("100"));
                BigDecimal bonusRatio = strategy.getBonusRatio().divide(new BigDecimal("100"));

                // 计算理论分配金额
                BigDecimal theoreticalRecharge = amount.multiply(rechargeRatio);
                BigDecimal theoreticalBonus = amount.multiply(bonusRatio);

                // 根据实际余额调整
                if (theoreticalRecharge.compareTo(rechargeBalance) <= 0 &&
                    theoreticalBonus.compareTo(bonusBalance) <= 0) {
                    rechargeAmount = theoreticalRecharge;
                    bonusAmount = theoreticalBonus;
                } else if (rechargeBalance.compareTo(amount) >= 0) {
                    rechargeAmount = amount;
                } else if (bonusBalance.compareTo(amount) >= 0) {
                    bonusAmount = amount;
                } else {
                    rechargeAmount = rechargeBalance;
                    bonusAmount = amount.subtract(rechargeAmount);
                }
                break;

            default:
                throw new ServiceException("不支持的消费策略类型：" + strategyType);
        }

        return new ConsumeAllocation(rechargeAmount, bonusAmount);
    }

    /**
     * 获取退款规则
     */
    private RefundRuleConfig getRefundRule(Long ruleId, BigDecimal amount) {
        if (ObjectUtil.isNotNull(ruleId)) {
            RefundRuleConfig rule = refundRuleConfigMapper.selectById(ruleId);
            if (ObjectUtil.isNotNull(rule) && "0".equals(rule.getStatus())) {
                return rule;
            }
        }

        // 根据订单金额匹配规则
        RefundRuleConfig matchedRule = refundRuleConfigMapper.selectRuleByOrderAmount(amount);
        if (ObjectUtil.isNotNull(matchedRule)) {
            return matchedRule;
        }

        // 使用默认规则
        RefundRuleConfig defaultRule = refundRuleConfigMapper.selectDefaultRule();
        if (ObjectUtil.isNull(defaultRule)) {
            throw new ServiceException("未找到可用的退款规则配置");
        }
        return defaultRule;
    }

    /**
     * 计算退款分配
     */
    private RefundAllocation calculateRefundAllocation(List<MemberWalletDetail> originalRecords,
                                                       BigDecimal refundAmount, RefundRuleConfig refundRule) {
        String refundType = refundRule.getRefundType();
        BigDecimal rechargeRefund = BigDecimal.ZERO;
        BigDecimal bonusRefund = BigDecimal.ZERO;

        switch (refundType) {
            case "RECHARGE_ONLY": // 仅退还充值金额
                rechargeRefund = refundAmount;
                break;

            case "PROPORTIONAL": // 按原消费比例退款
                BigDecimal totalRechargeUsed = BigDecimal.ZERO;
                BigDecimal totalBonusUsed = BigDecimal.ZERO;

                // 统计原消费记录中的充值和赠送金额使用情况
                for (MemberWalletDetail record : originalRecords) {
                    if ("CONSUME".equals(record.getBusinessType())) {
                        if ("RECHARGE".equals(record.getAmountType())) {
                            totalRechargeUsed = totalRechargeUsed.add(record.getChangeAmount().abs());
                        } else if ("BONUS".equals(record.getAmountType())) {
                            totalBonusUsed = totalBonusUsed.add(record.getChangeAmount().abs());
                        }
                    }
                }

                BigDecimal totalUsed = totalRechargeUsed.add(totalBonusUsed);
                if (totalUsed.compareTo(BigDecimal.ZERO) > 0) {
                    // 按比例计算退款
                    BigDecimal rechargeRatio = totalRechargeUsed.divide(totalUsed, 4, BigDecimal.ROUND_HALF_UP);
                    BigDecimal bonusRatio = totalBonusUsed.divide(totalUsed, 4, BigDecimal.ROUND_HALF_UP);

                    rechargeRefund = refundAmount.multiply(rechargeRatio).setScale(2, BigDecimal.ROUND_HALF_UP);
                    bonusRefund = refundAmount.multiply(bonusRatio).setScale(2, BigDecimal.ROUND_HALF_UP);

                    // 处理精度误差
                    BigDecimal actualTotal = rechargeRefund.add(bonusRefund);
                    if (actualTotal.compareTo(refundAmount) != 0) {
                        BigDecimal diff = refundAmount.subtract(actualTotal);
                        rechargeRefund = rechargeRefund.add(diff);
                    }
                } else {
                    rechargeRefund = refundAmount;
                }
                break;

            case "FULL_REFUND": // 全额退款（包含赠送）
                // 按系统默认策略分配（优先退还充值金额）
                rechargeRefund = refundAmount;
                bonusRefund = BigDecimal.ZERO;
                break;

            default:
                throw new ServiceException("不支持的退款类型：" + refundType);
        }

        return new RefundAllocation(rechargeRefund, bonusRefund);
    }

    /**
     * 更新会员充值统计
     */
    private void updateMemberStatisticsForRecharge(Long memberId, BigDecimal amount) {
        try {
            LambdaQueryWrapper<MemberStatistics> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MemberStatistics::getMemberId, memberId);
            MemberStatistics statistics = statisticsMapper.selectOne(wrapper);

            if (ObjectUtil.isNull(statistics)) {
                // 如果统计记录不存在，创建一个
                statistics = new MemberStatistics();
                statistics.setMemberId(memberId);
                statistics.setTotalRechargeAmount(amount);
                statistics.setTotalRechargeCount(1);
                statistics.setTotalConsumeAmount(BigDecimal.ZERO);
                statistics.setTotalConsumeCount(0);
                statisticsMapper.insert(statistics);
            } else {
                // 更新统计数据
                statistics.setTotalRechargeAmount(statistics.getTotalRechargeAmount().add(amount));
                statistics.setTotalRechargeCount(statistics.getTotalRechargeCount() + 1);
                statisticsMapper.updateById(statistics);
            }

            log.debug("更新会员充值统计成功，会员ID：{}，充值金额：{}", memberId, amount);
        } catch (Exception e) {
            log.error("更新会员充值统计失败", e);
            // 统计更新失败不影响主流程
        }
    }

    /**
     * 更新会员消费统计
     */
    private void updateMemberStatisticsForConsume(Long memberId, BigDecimal amount) {
        try {
            LambdaQueryWrapper<MemberStatistics> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MemberStatistics::getMemberId, memberId);
            MemberStatistics statistics = statisticsMapper.selectOne(wrapper);

            if (ObjectUtil.isNull(statistics)) {
                // 如果统计记录不存在，创建一个
                statistics = new MemberStatistics();
                statistics.setMemberId(memberId);
                statistics.setTotalConsumeAmount(amount);
                statistics.setTotalConsumeCount(1);
                statistics.setTotalRechargeAmount(BigDecimal.ZERO);
                statistics.setTotalRechargeCount(0);
                statisticsMapper.insert(statistics);
            } else {
                // 更新统计数据
                statistics.setTotalConsumeAmount(statistics.getTotalConsumeAmount().add(amount));
                statistics.setTotalConsumeCount(statistics.getTotalConsumeCount() + 1);
                statisticsMapper.updateById(statistics);
            }

            log.debug("更新会员消费统计成功，会员ID：{}，消费金额：{}", memberId, amount);
        } catch (Exception e) {
            log.error("更新会员消费统计失败", e);
            // 统计更新失败不影响主流程
        }
    }

    private WalletOperationResultVo buildSuccessResult(Long memberId, String orderNo, BigDecimal amount) {
        WalletOperationResultVo result = new WalletOperationResultVo();
        result.setSuccess(true);
        result.setMemberId(memberId);
        result.setOrderNo(orderNo);
        result.setOperationAmount(amount);
        return result;
    }

    private WalletOperationResultVo buildFailureResult(Long memberId, String orderNo, BigDecimal amount, String errorMessage) {
        WalletOperationResultVo result = new WalletOperationResultVo();
        result.setSuccess(false);
        result.setMemberId(memberId);
        result.setOrderNo(orderNo);
        result.setOperationAmount(amount);
        result.setErrorMessage(errorMessage);
        return result;
    }

    /**
     * 通用重试执行方法
     */
    private <T> T executeWithRetry(java.util.function.Supplier<T> operation) {
        int maxAttempts = 3;
        int attempt = 0;
        Exception lastException = null;

        while (attempt < maxAttempts) {
            try {
                return operation.get();
            } catch (Exception e) {
                lastException = e;
                attempt++;

                if (attempt >= maxAttempts) {
                    log.error("重试执行失败，已达到最大重试次数：{}", maxAttempts, e);
                    break;
                }

                // 等待一段时间后重试
                try {
                    Thread.sleep(100 * attempt); // 递增等待时间
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new ServiceException("操作被中断");
                }

                log.warn("操作失败，正在进行第{}次重试", attempt, e);
            }
        }

        if (lastException instanceof ServiceException) {
            throw (ServiceException) lastException;
        } else {
            throw new ServiceException("操作执行失败：" + lastException.getMessage());
        }
    }

    // 内部类：消费分配结果
    private static class ConsumeAllocation {
        private final BigDecimal rechargeAmount;
        private final BigDecimal bonusAmount;

        public ConsumeAllocation(BigDecimal rechargeAmount, BigDecimal bonusAmount) {
            this.rechargeAmount = rechargeAmount;
            this.bonusAmount = bonusAmount;
        }

        public BigDecimal getRechargeAmount() {
            return rechargeAmount;
        }

        public BigDecimal getBonusAmount() {
            return bonusAmount;
        }
    }

    // 内部类：退款分配结果
    private static class RefundAllocation {
        private final BigDecimal rechargeAmount;
        private final BigDecimal bonusAmount;

        public RefundAllocation(BigDecimal rechargeAmount, BigDecimal bonusAmount) {
            this.rechargeAmount = rechargeAmount;
            this.bonusAmount = bonusAmount;
        }

        public BigDecimal getRechargeAmount() {
            return rechargeAmount;
        }

        public BigDecimal getBonusAmount() {
            return bonusAmount;
        }
    }

}
