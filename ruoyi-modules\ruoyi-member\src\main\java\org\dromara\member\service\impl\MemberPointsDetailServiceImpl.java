package org.dromara.member.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.member.domain.MemberPointsDetail;
import org.dromara.member.domain.bo.MemberPointsDetailBo;
import org.dromara.member.domain.vo.MemberPointsDetailVo;
import org.dromara.member.mapper.MemberInfoMapper;
import org.dromara.member.mapper.MemberPointsDetailMapper;
import org.dromara.member.service.IMemberPointsDetailService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 积分明细记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@RequiredArgsConstructor
@Service
public class MemberPointsDetailServiceImpl implements IMemberPointsDetailService {

    private final MemberPointsDetailMapper baseMapper;
    private final MemberInfoMapper memberInfoMapper;

    /**
     * 查询积分明细记录
     */
    @Override
    public MemberPointsDetailVo queryById(Long id){
        MemberPointsDetailVo memberPointsDetailVo = baseMapper.selectVoById(id);
        memberPointsDetailVo.setMemberName(memberInfoMapper.selectVoById(memberPointsDetailVo.getMemberId()).getNickname());
        return memberPointsDetailVo;
    }

    /**
     * 查询积分明细记录列表
     */
    @Override
    public TableDataInfo<MemberPointsDetailVo> queryPageList(MemberPointsDetailBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MemberPointsDetail> lqw = buildQueryWrapper(bo);
        Page<MemberPointsDetailVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        result.getRecords().forEach(item -> {
            item.setMemberName(memberInfoMapper.selectVoById(item.getMemberId()).getNickname());
        });
        return TableDataInfo.build(result);
    }

    /**
     * 查询积分明细记录列表
     */
    @Override
    public List<MemberPointsDetailVo> queryList(MemberPointsDetailBo bo) {
        LambdaQueryWrapper<MemberPointsDetail> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MemberPointsDetail> buildQueryWrapper(MemberPointsDetailBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MemberPointsDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getMemberId() != null, MemberPointsDetail::getMemberId, bo.getMemberId());
        lqw.like(StringUtils.isNotBlank(bo.getOrderNo()), MemberPointsDetail::getOrderNo, bo.getOrderNo());
        lqw.eq(bo.getBusinessType() != null, MemberPointsDetail::getBusinessType, bo.getBusinessType());
        lqw.eq(bo.getRuleId() != null, MemberPointsDetail::getRuleId, bo.getRuleId());
        lqw.orderByDesc(MemberPointsDetail::getCreateTime);
        return lqw;
    }

    /**
     * 新增积分明细记录
     */
    @Override
    public Boolean insertByBo(MemberPointsDetailBo bo) {
        MemberPointsDetail add = MapstructUtils.convert(bo, MemberPointsDetail.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改积分明细记录
     */
    @Override
    public Boolean updateByBo(MemberPointsDetailBo bo) {
        MemberPointsDetail update = MapstructUtils.convert(bo, MemberPointsDetail.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MemberPointsDetail entity){
        // 校验积分变动数据
        if (entity.getPointsChange() == null || entity.getPointsChange() == 0) {
            throw new RuntimeException("积分变动数量不能为空或0");
        }

        // 校验积分余额数据
        if (entity.getPointsBefore() == null || entity.getPointsBefore() < 0) {
            throw new RuntimeException("变动前积分余额不能为空或小于0");
        }

        if (entity.getPointsAfter() == null || entity.getPointsAfter() < 0) {
            throw new RuntimeException("变动后积分余额不能为空或小于0");
        }

        // 校验积分变动逻辑
        Long expectedAfter = entity.getPointsBefore() + entity.getPointsChange();
        if (!expectedAfter.equals(entity.getPointsAfter())) {
            throw new RuntimeException("积分变动计算错误");
        }
    }

    /**
     * 批量删除积分明细记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 记录积分变动
     */
    @Override
    public Boolean recordPointsChange(MemberPointsDetailBo detailBo) {
        return insertByBo(detailBo);
    }

    /**
     * 获取会员积分历史
     */
    @Override
    public TableDataInfo<MemberPointsDetailVo> getMemberPointsHistory(Long memberId, PageQuery pageQuery) {
        MemberPointsDetailBo bo = new MemberPointsDetailBo();
        bo.setMemberId(memberId);
        return queryPageList(bo, pageQuery);
    }
}
