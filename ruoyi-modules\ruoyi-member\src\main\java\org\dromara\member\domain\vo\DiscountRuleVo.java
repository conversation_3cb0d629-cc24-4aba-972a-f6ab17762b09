package org.dromara.member.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.member.domain.DiscountRule;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 优惠规则配置视图对象 discount_rule
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = DiscountRule.class)
public class DiscountRuleVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 规则名称
     */
    @ExcelProperty(value = "规则名称")
    private String ruleName;

    /**
     * 规则类型：1-满减，2-折扣，3-买赠，4-阶梯满减
     */
    @ExcelProperty(value = "规则类型：1-满减，2-折扣，3-买赠，4-阶梯满减")
    private String ruleType;

    /**
     * 最小消费金额
     */
    @ExcelProperty(value = "最小消费金额")
    private BigDecimal minAmount;

    /**
     * 最大消费金额（null表示无上限）
     */
    @ExcelProperty(value = "最大消费金额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "n=ull表示无上限")
    private BigDecimal maxAmount;

    /**
     * 优惠值（满减金额或折扣比例）
     */
    @ExcelProperty(value = "优惠值", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "满=减金额或折扣比例")
    private BigDecimal discountValue;

    /**
     * 最大优惠金额（折扣时使用）
     */
    @ExcelProperty(value = "最大优惠金额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "折=扣时使用")
    private BigDecimal maxDiscountAmount;

    /**
     * 适用商品（JSON格式，null表示全商品）
     */
    @ExcelProperty(value = "适用商品", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "J=SON格式，null表示全商品")
    private String applicableProducts;

    /**
     * 排除商品（JSON格式）
     */
    @ExcelProperty(value = "排除商品", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "J=SON格式")
    private String excludeProducts;

    /**
     * 用户限制类型：0-无限制，1-每人限用，2-新用户专享
     */
    @ExcelProperty(value = "用户限制类型：0-无限制，1-每人限用，2-新用户专享")
    private String userLimitType;

    /**
     * 每人限用次数
     */
    @ExcelProperty(value = "每人限用次数")
    private Long userLimitCount;

    /**
     * 总使用次数限制
     */
    @ExcelProperty(value = "总使用次数限制")
    private Long totalLimitCount;

    /**
     * 已使用次数
     */
    @ExcelProperty(value = "已使用次数")
    private Long usedCount;

    /**
     * 规则生效开始时间
     */
    @ExcelProperty(value = "规则生效开始时间")
    private Date startTime;

    /**
     * 规则生效结束时间
     */
    @ExcelProperty(value = "规则生效结束时间")
    private Date endTime;

    /**
     * 状态：0-启用，1-禁用
     */
    @ExcelProperty(value = "状态：0-启用，1-禁用")
    private String status;

    /**
     * 优先级（数字越大优先级越高）
     */
    @ExcelProperty(value = "优先级", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "数=字越大优先级越高")
    private Long priority;

    /**
     * 是否可叠加：0-不可叠加，1-可叠加
     */
    @ExcelProperty(value = "是否可叠加：0-不可叠加，1-可叠加")
    private String canStack;

    /**
     * 备注说明
     */
    @ExcelProperty(value = "备注说明")
    private String remark;


}
