package org.dromara.member.domain.bo;

import org.dromara.member.domain.MemberDiscountStats;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;

/**
 * 会员优惠使用统计业务对象 member_discount_stats
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MemberDiscountStats.class, reverseConvertGenerate = false)
public class MemberDiscountStatsBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 已使用次数
     */
    @NotNull(message = "已使用次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long usedCount;

    /**
     * 最后使用时间
     */
    @NotNull(message = "最后使用时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date lastUsedTime;


}
