-- 优惠体系相关表结构

-- 1. 优惠规则配置表
CREATE TABLE `discount_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `rule_type` char(1) NOT NULL DEFAULT '1' COMMENT '规则类型：1-满减，2-折扣，3-买赠，4-阶梯满减',
  `min_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最小消费金额',
  `max_amount` decimal(10,2) DEFAULT NULL COMMENT '最大消费金额（null表示无上限）',
  `discount_value` decimal(10,2) NOT NULL COMMENT '优惠值（满减金额或折扣比例）',
  `max_discount_amount` decimal(10,2) DEFAULT NULL COMMENT '最大优惠金额（折扣时使用）',
  `applicable_products` text COMMENT '适用商品（JSON格式，null表示全商品）',
  `exclude_products` text COMMENT '排除商品（JSON格式）',
  `user_limit_type` char(1) DEFAULT '0' COMMENT '用户限制类型：0-无限制，1-每人限用，2-新用户专享',
  `user_limit_count` int(11) DEFAULT NULL COMMENT '每人限用次数',
  `total_limit_count` int(11) DEFAULT NULL COMMENT '总使用次数限制',
  `used_count` int(11) DEFAULT '0' COMMENT '已使用次数',
  `start_time` datetime DEFAULT NULL COMMENT '规则生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '规则生效结束时间',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态：0-启用，1-禁用',
  `priority` int(11) NOT NULL DEFAULT '0' COMMENT '优先级（数字越大优先级越高）',
  `can_stack` char(1) DEFAULT '0' COMMENT '是否可叠加：0-不可叠加，1-可叠加',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_status_priority` (`status`, `priority`),
  KEY `idx_amount_range` (`min_amount`, `max_amount`),
  KEY `idx_time_range` (`start_time`, `end_time`),
  KEY `idx_rule_type` (`rule_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠规则配置表';

-- 2. 阶梯优惠配置表
CREATE TABLE `discount_ladder` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_id` bigint(20) NOT NULL COMMENT '关联规则ID',
  `min_amount` decimal(10,2) NOT NULL COMMENT '最小金额',
  `max_amount` decimal(10,2) DEFAULT NULL COMMENT '最大金额',
  `discount_value` decimal(10,2) NOT NULL COMMENT '优惠值',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_rule_id` (`rule_id`),
  KEY `idx_amount_range` (`min_amount`, `max_amount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='阶梯优惠配置表';

-- 3. 优惠使用记录表
CREATE TABLE `discount_usage_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `order_no` varchar(64) NOT NULL COMMENT '订单号',
  `rule_id` bigint(20) NOT NULL COMMENT '使用的规则ID',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称（冗余字段）',
  `rule_type` char(1) NOT NULL COMMENT '规则类型',
  `original_amount` decimal(10,2) NOT NULL COMMENT '原始金额',
  `discount_amount` decimal(10,2) NOT NULL COMMENT '优惠金额',
  `final_amount` decimal(10,2) NOT NULL COMMENT '最终金额',
  `applicable_products` text COMMENT '适用商品信息（JSON格式）',
  `usage_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
  `status` char(1) DEFAULT '1' COMMENT '状态：1-已使用，2-已退款',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_rule_id` (`rule_id`),
  KEY `idx_usage_time` (`usage_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠使用记录表';

-- 4. 会员优惠使用统计表
CREATE TABLE `member_discount_stats` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `rule_id` bigint(20) NOT NULL COMMENT '规则ID',
  `used_count` int(11) DEFAULT '0' COMMENT '已使用次数',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_member_rule` (`member_id`, `rule_id`, `tenant_id`),
  KEY `idx_rule_id` (`rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员优惠使用统计表';

-- 插入示例优惠规则数据
INSERT INTO `discount_rule` (`rule_name`, `rule_type`, `min_amount`, `discount_value`, `priority`, `status`, `remark`) VALUES
('满100减20', '1', 100.00, 20.00, 1, '0', '消费满100元减20元'),
('满200减40', '1', 200.00, 40.00, 2, '0', '消费满200元减40元'),
('全场8折', '2', 0.01, 0.80, 1, '0', '全场商品8折优惠'),
('新用户专享9折', '2', 0.01, 0.90, 3, '0', '新用户专享9折优惠');

-- 更新新用户专享规则的用户限制
UPDATE `discount_rule` SET `user_limit_type` = '2', `user_limit_count` = 1 WHERE `rule_name` = '新用户专享9折';

-- 插入阶梯优惠示例数据（满100减10，满200减30，满300减50）
INSERT INTO `discount_rule` (`rule_name`, `rule_type`, `min_amount`, `discount_value`, `priority`, `status`, `remark`) VALUES
('阶梯满减', '4', 100.00, 0.00, 4, '0', '阶梯式满减优惠');

-- 获取刚插入的阶梯规则ID并插入阶梯配置
SET @ladder_rule_id = LAST_INSERT_ID();
INSERT INTO `discount_ladder` (`rule_id`, `min_amount`, `max_amount`, `discount_value`, `sort_order`) VALUES
(@ladder_rule_id, 100.00, 199.99, 10.00, 1),
(@ladder_rule_id, 200.00, 299.99, 30.00, 2),
(@ladder_rule_id, 300.00, NULL, 50.00, 3);
