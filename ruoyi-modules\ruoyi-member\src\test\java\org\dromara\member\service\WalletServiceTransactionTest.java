package org.dromara.member.service;

import org.dromara.member.domain.dto.ConsumeRequestDto;
import org.dromara.member.domain.dto.RefundRequestDto;
import org.dromara.member.domain.vo.WalletOperationResultVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 钱包服务事务测试
 * 主要测试事务回滚异常修复
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class WalletServiceTransactionTest {

    @Autowired
    private IWalletService walletService;

    @Test
    public void testConsumeWithInsufficientBalance() {
        // 测试余额不足的情况，应该返回失败结果而不是抛出事务异常
        ConsumeRequestDto request = new ConsumeRequestDto();
        request.setMemberId(999L); // 假设这个会员不存在或余额不足
        request.setConsumeAmount(new BigDecimal("10000.00")); // 大金额消费
        request.setOrderNo("TEST_ORDER_" + System.currentTimeMillis());
        request.setStrategyId(1L);

        // 这个调用不应该抛出UnexpectedRollbackException
        WalletOperationResultVo result = walletService.consume(request);
        
        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertTrue(result.getErrorMessage().contains("失败"));
    }

    @Test
    public void testRefundWithNonExistentOrder() {
        // 测试退款不存在订单的情况
        RefundRequestDto request = new RefundRequestDto();
        request.setMemberId(999L);
        request.setOriginalOrderNo("NON_EXISTENT_ORDER");
        request.setRefundAmount(new BigDecimal("100.00"));
        request.setRuleId(1L);

        // 这个调用不应该抛出UnexpectedRollbackException
        WalletOperationResultVo result = walletService.refund(request);
        
        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertTrue(result.getErrorMessage().contains("失败"));
    }

    @Test
    public void testConsumeWithInvalidMember() {
        // 测试无效会员的情况
        ConsumeRequestDto request = new ConsumeRequestDto();
        request.setMemberId(-1L); // 无效的会员ID
        request.setConsumeAmount(new BigDecimal("100.00"));
        request.setOrderNo("TEST_ORDER_" + System.currentTimeMillis());
        request.setStrategyId(1L);

        // 这个调用不应该抛出UnexpectedRollbackException
        WalletOperationResultVo result = walletService.consume(request);
        
        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertTrue(result.getErrorMessage().contains("失败"));
    }

}
