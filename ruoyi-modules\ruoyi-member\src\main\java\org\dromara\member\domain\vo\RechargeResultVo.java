package org.dromara.member.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 充值结果VO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class RechargeResultVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 充值订单号
     */
    private String orderNo;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 充值金额
     */
    private BigDecimal rechargeAmount;

    /**
     * 赠送金额
     */
    private BigDecimal bonusAmount;

    /**
     * 充值前总余额
     */
    private BigDecimal balanceBefore;

    /**
     * 充值后总余额
     */
    private BigDecimal balanceAfter;

    /**
     * 匹配的活动名称
     */
    private String activityName;

    /**
     * 支付状态
     */
    private String paymentStatus;

    /**
     * 支付地址（如果需要跳转支付）
     */
    private String paymentUrl;

} 