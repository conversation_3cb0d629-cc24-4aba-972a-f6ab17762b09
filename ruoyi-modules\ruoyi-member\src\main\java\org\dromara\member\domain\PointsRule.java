package org.dromara.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.member.domain.base.SimpleBaseEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 积分规则配置对象 points_rule
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("points_rule")
public class PointsRule extends SimpleBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 最小消费金额
     */
    private BigDecimal minAmount;

    /**
     * 最大消费金额（null表示无上限）
     */
    private BigDecimal maxAmount;

    /**
     * 积分比例（每元获得积分数）
     */
    private BigDecimal pointsRatio;

    /**
     * 固定积分数（优先级高于比例）
     */
    private Integer fixedPoints;

    /**
     * 规则生效开始时间
     */
    private Date startTime;

    /**
     * 规则生效结束时间
     */
    private Date endTime;

    /**
     * 状态：0-启用，1-禁用
     */
    private String status;

    /**
     * 优先级（数字越大优先级越高）
     */
    private Integer priority;

    /**
     * 备注说明
     */
    private String remark;

}
