package org.dromara.member.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.member.domain.PointsRule;
import org.dromara.member.domain.bo.PointsRuleBo;
import org.dromara.member.domain.vo.PointsRuleVo;
import org.dromara.member.mapper.PointsRuleMapper;
import org.dromara.member.service.IPointsRuleService;
import org.dromara.member.service.IPointsCacheService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 积分规则配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@RequiredArgsConstructor
@Service
public class PointsRuleServiceImpl implements IPointsRuleService {

    private final PointsRuleMapper baseMapper;
    private final IPointsCacheService pointsCacheService;

    /**
     * 查询积分规则配置
     */
    @Override
    public PointsRuleVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询积分规则配置列表
     */
    @Override
    public TableDataInfo<PointsRuleVo> queryPageList(PointsRuleBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PointsRule> lqw = buildQueryWrapper(bo);
        Page<PointsRuleVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询积分规则配置列表
     */
    @Override
    public List<PointsRuleVo> queryList(PointsRuleBo bo) {
        LambdaQueryWrapper<PointsRule> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PointsRule> buildQueryWrapper(PointsRuleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PointsRule> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getRuleName()), PointsRule::getRuleName, bo.getRuleName());
        lqw.eq(bo.getMinAmount() != null, PointsRule::getMinAmount, bo.getMinAmount());
        lqw.eq(bo.getMaxAmount() != null, PointsRule::getMaxAmount, bo.getMaxAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PointsRule::getStatus, bo.getStatus());
        lqw.orderByDesc(PointsRule::getPriority);
        return lqw;
    }

    /**
     * 新增积分规则配置
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(PointsRuleBo bo) {
        PointsRule add = MapstructUtils.convert(bo, PointsRule.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            // 清除缓存
            pointsCacheService.clearRulesCache();
        }
        return flag;
    }

    /**
     * 修改积分规则配置
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(PointsRuleBo bo) {
        PointsRule update = MapstructUtils.convert(bo, PointsRule.class);
        validEntityBeforeSave(update);
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            // 清除缓存
            pointsCacheService.clearRulesCache();
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PointsRule entity){
        // 校验金额范围
        if (entity.getMaxAmount() != null && entity.getMinAmount() != null) {
            if (entity.getMaxAmount().compareTo(entity.getMinAmount()) <= 0) {
                throw new RuntimeException("最大消费金额必须大于最小消费金额");
            }
        }

        // 校验时间范围
        if (entity.getEndTime() != null && entity.getStartTime() != null) {
            if (entity.getEndTime().before(entity.getStartTime())) {
                throw new RuntimeException("结束时间必须大于开始时间");
            }
        }

        // 校验积分设置：固定积分和比例积分至少设置一个
        if (entity.getFixedPoints() == null && entity.getPointsRatio() == null) {
            throw new RuntimeException("固定积分数和积分比例至少设置一个");
        }
    }

    /**
     * 批量删除积分规则配置
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 获取有效的积分规则列表
     */
    @Override
    public List<PointsRuleVo> getActiveRules() {
        // 先从缓存获取
        List<PointsRuleVo> cachedRules = pointsCacheService.getCachedActiveRules();
        if (CollUtil.isNotEmpty(cachedRules)) {
            return cachedRules;
        }

        // 缓存未命中，从数据库查询 - 使用自定义方法避免字段不匹配问题
        Date now = new Date();
         List<PointsRuleVo> rules = baseMapper.selectActiveRules("0", now);

        // 缓存查询结果
        if (CollUtil.isNotEmpty(rules)) {
            pointsCacheService.cacheActiveRules(rules);
        }

        return rules;
    }

    /**
     * 根据消费金额计算积分
     */
    @Override
    public Long calculatePoints(BigDecimal amount) {
        PointsRuleVo rule = findBestRule(amount);
        if (rule == null) {
            return 0L;
        }

        // 优先使用固定积分
        if (rule.getFixedPoints() != null) {
            return rule.getFixedPoints().longValue();
        }

        // 使用积分比例计算
        if (rule.getPointsRatio() != null) {
            return amount.multiply(rule.getPointsRatio()).longValue();
        }

        return 0L;
    }

    /**
     * 根据消费金额找到最匹配的规则
     */
    @Override
    public PointsRuleVo findBestRule(BigDecimal amount) {
        List<PointsRuleVo> activeRules = getActiveRules();
        if (CollUtil.isEmpty(activeRules)) {
            return null;
        }

        for (PointsRuleVo rule : activeRules) {
            // 检查最小金额
            if (amount.compareTo(rule.getMinAmount()) < 0) {
                continue;
            }

            // 检查最大金额（null表示无上限）
            if (rule.getMaxAmount() != null && amount.compareTo(rule.getMaxAmount()) > 0) {
                continue;
            }

            return rule;
        }

        return null;
    }
}
