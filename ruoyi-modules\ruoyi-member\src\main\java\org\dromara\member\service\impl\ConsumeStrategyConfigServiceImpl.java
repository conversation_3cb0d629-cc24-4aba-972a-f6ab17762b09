package org.dromara.member.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.member.domain.ConsumeStrategyConfig;
import org.dromara.member.mapper.ConsumeStrategyConfigMapper;
import org.dromara.member.service.IConsumeStrategyConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 消费策略配置服务实现类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ConsumeStrategyConfigServiceImpl implements IConsumeStrategyConfigService {

    private final ConsumeStrategyConfigMapper consumeStrategyConfigMapper;

    @Override
    public IPage<ConsumeStrategyConfig> selectPage(Page<ConsumeStrategyConfig> page, ConsumeStrategyConfig config) {
        LambdaQueryWrapper<ConsumeStrategyConfig> wrapper = buildQueryWrapper(config);
        wrapper.orderByAsc(ConsumeStrategyConfig::getIsDefault);
        wrapper.orderByDesc(ConsumeStrategyConfig::getCreateTime);
        return consumeStrategyConfigMapper.selectPage(page, wrapper);
    }

    @Override
    public List<ConsumeStrategyConfig> selectList(ConsumeStrategyConfig config) {
        LambdaQueryWrapper<ConsumeStrategyConfig> wrapper = buildQueryWrapper(config);
        wrapper.orderByAsc(ConsumeStrategyConfig::getIsDefault);
        wrapper.orderByDesc(ConsumeStrategyConfig::getCreateTime);
        return consumeStrategyConfigMapper.selectList(wrapper);
    }

    @Override
    public ConsumeStrategyConfig selectById(Long id) {
        if (ObjectUtil.isNull(id)) {
            throw new ServiceException("配置ID不能为空");
        }
        return consumeStrategyConfigMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insert(ConsumeStrategyConfig config) {
        // 验证配置数据
        validateConfig(config);
        
        // 设置默认值
        if (ObjectUtil.isEmpty(config.getStatus())) {
            config.setStatus("0"); // 默认启用
        }
        if (ObjectUtil.isEmpty(config.getIsDefault())) {
            config.setIsDefault("0"); // 默认非默认策略
        }
        
        // 如果设置为默认策略，需要取消其他默认策略
        if ("1".equals(config.getIsDefault())) {
            clearOtherDefaultStrategies();
        }
        
        int result = consumeStrategyConfigMapper.insert(config);
        if (result > 0) {
            log.info("新增消费策略配置成功：{}", config.getStrategyName());
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(ConsumeStrategyConfig config) {
        if (ObjectUtil.isNull(config.getId())) {
            throw new ServiceException("配置ID不能为空");
        }
        
        // 验证配置是否存在
        ConsumeStrategyConfig existConfig = selectById(config.getId());
        if (ObjectUtil.isNull(existConfig)) {
            throw new ServiceException("配置不存在");
        }
        
        // 验证配置数据
        validateConfig(config);
        
        // 如果设置为默认策略，需要取消其他默认策略
        if ("1".equals(config.getIsDefault())) {
            clearOtherDefaultStrategies(config.getId());
        }
        
        int result = consumeStrategyConfigMapper.updateById(config);
        if (result > 0) {
            log.info("更新消费策略配置成功：{}", config.getStrategyName());
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        if (ObjectUtil.isNull(id)) {
            throw new ServiceException("配置ID不能为空");
        }
        
        ConsumeStrategyConfig config = selectById(id);
        if (ObjectUtil.isNull(config)) {
            throw new ServiceException("配置不存在");
        }
        
        // 不能删除默认策略
        if ("1".equals(config.getIsDefault())) {
            throw new ServiceException("不能删除默认策略");
        }
        
        int result = consumeStrategyConfigMapper.deleteById(id);
        if (result > 0) {
            log.info("删除消费策略配置成功：{}", config.getStrategyName());
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteBatch(List<Long> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            throw new ServiceException("配置ID列表不能为空");
        }
        
        // 检查是否包含默认策略
        for (Long id : ids) {
            ConsumeStrategyConfig config = selectById(id);
            if (ObjectUtil.isNotNull(config) && "1".equals(config.getIsDefault())) {
                throw new ServiceException("不能删除默认策略：" + config.getStrategyName());
            }
        }
        
        int result = consumeStrategyConfigMapper.deleteBatchIds(ids);
        if (result > 0) {
            log.info("批量删除消费策略配置成功，数量：{}", result);
            return true;
        }
        return false;
    }

    @Override
    public ConsumeStrategyConfig selectDefaultStrategy() {
        return consumeStrategyConfigMapper.selectDefaultStrategy();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeStatus(Long id, String status) {
        if (ObjectUtil.isNull(id)) {
            throw new ServiceException("配置ID不能为空");
        }
        
        if (StringUtils.isBlank(status)) {
            throw new ServiceException("状态不能为空");
        }
        
        ConsumeStrategyConfig existConfig = selectById(id);
        if (ObjectUtil.isNull(existConfig)) {
            throw new ServiceException("配置不存在");
        }
        
        // 如果是默认策略，不能禁用
        if ("1".equals(existConfig.getIsDefault()) && "1".equals(status)) {
            throw new ServiceException("不能禁用默认策略");
        }
        
        ConsumeStrategyConfig config = new ConsumeStrategyConfig();
        config.setId(id);
        config.setStatus(status);
        
        int result = consumeStrategyConfigMapper.updateById(config);
        if (result > 0) {
            log.info("更新消费策略配置状态成功，ID：{}，状态：{}", id, status);
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setDefaultStrategy(Long id) {
        if (ObjectUtil.isNull(id)) {
            throw new ServiceException("配置ID不能为空");
        }
        
        ConsumeStrategyConfig existConfig = selectById(id);
        if (ObjectUtil.isNull(existConfig)) {
            throw new ServiceException("配置不存在");
        }
        
        // 验证策略状态是否启用
        if (!"0".equals(existConfig.getStatus())) {
            throw new ServiceException("只能设置启用状态的策略为默认策略");
        }
        
        // 取消其他默认策略
        clearOtherDefaultStrategies(id);
        
        // 设置为默认策略
        ConsumeStrategyConfig config = new ConsumeStrategyConfig();
        config.setId(id);
        config.setIsDefault("1");
        
        int result = consumeStrategyConfigMapper.updateById(config);
        if (result > 0) {
            log.info("设置默认消费策略成功，ID：{}", id);
            return true;
        }
        return false;
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<ConsumeStrategyConfig> buildQueryWrapper(ConsumeStrategyConfig config) {
        LambdaQueryWrapper<ConsumeStrategyConfig> wrapper = new LambdaQueryWrapper<>();
        
        if (ObjectUtil.isNotNull(config)) {
            wrapper.like(StringUtils.isNotBlank(config.getStrategyName()), 
                        ConsumeStrategyConfig::getStrategyName, config.getStrategyName());
            wrapper.eq(StringUtils.isNotBlank(config.getStrategyType()), 
                      ConsumeStrategyConfig::getStrategyType, config.getStrategyType());
            wrapper.eq(StringUtils.isNotBlank(config.getStatus()), 
                      ConsumeStrategyConfig::getStatus, config.getStatus());
            wrapper.eq(StringUtils.isNotBlank(config.getIsDefault()), 
                      ConsumeStrategyConfig::getIsDefault, config.getIsDefault());
        }
        
        return wrapper;
    }

    /**
     * 验证配置数据
     */
    private void validateConfig(ConsumeStrategyConfig config) {
        if (StringUtils.isBlank(config.getStrategyName())) {
            throw new ServiceException("策略名称不能为空");
        }
        
        if (StringUtils.isBlank(config.getStrategyType())) {
            throw new ServiceException("策略类型不能为空");
        }
        
        // 验证策略类型
        if (!"RECHARGE_FIRST".equals(config.getStrategyType()) && 
            !"BONUS_FIRST".equals(config.getStrategyType()) && 
            !"RATIO".equals(config.getStrategyType())) {
            throw new ServiceException("不支持的策略类型");
        }
        
        // 如果是按比例策略，验证比例设置
        if ("RATIO".equals(config.getStrategyType())) {
            if (ObjectUtil.isNull(config.getRechargeRatio()) || 
                ObjectUtil.isNull(config.getBonusRatio())) {
                throw new ServiceException("按比例策略必须设置充值比例和赠送比例");
            }
            
            if (config.getRechargeRatio().compareTo(BigDecimal.ZERO) < 0 || 
                config.getRechargeRatio().compareTo(new BigDecimal("100")) > 0) {
                throw new ServiceException("充值比例必须在0-100之间");
            }
            
            if (config.getBonusRatio().compareTo(BigDecimal.ZERO) < 0 || 
                config.getBonusRatio().compareTo(new BigDecimal("100")) > 0) {
                throw new ServiceException("赠送比例必须在0-100之间");
            }
            
            // 比例之和应该等于100
            BigDecimal totalRatio = config.getRechargeRatio().add(config.getBonusRatio());
            if (totalRatio.compareTo(new BigDecimal("100")) != 0) {
                throw new ServiceException("充值比例和赠送比例之和必须等于100");
            }
        }
        
        // 检查策略名称是否重复
        if (isStrategyNameExists(config.getStrategyName(), config.getId())) {
            throw new ServiceException("策略名称已存在");
        }
    }

    /**
     * 检查策略名称是否已存在
     */
    private boolean isStrategyNameExists(String strategyName, Long excludeId) {
        LambdaQueryWrapper<ConsumeStrategyConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ConsumeStrategyConfig::getStrategyName, strategyName);
        if (ObjectUtil.isNotNull(excludeId)) {
            wrapper.ne(ConsumeStrategyConfig::getId, excludeId);
        }
        return consumeStrategyConfigMapper.selectCount(wrapper) > 0;
    }

    /**
     * 取消其他默认策略
     */
    private void clearOtherDefaultStrategies() {
        clearOtherDefaultStrategies(null);
    }

    /**
     * 取消其他默认策略
     */
    private void clearOtherDefaultStrategies(Long excludeId) {
        LambdaQueryWrapper<ConsumeStrategyConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ConsumeStrategyConfig::getIsDefault, "1");
        if (ObjectUtil.isNotNull(excludeId)) {
            wrapper.ne(ConsumeStrategyConfig::getId, excludeId);
        }
        
        List<ConsumeStrategyConfig> defaultStrategies = consumeStrategyConfigMapper.selectList(wrapper);
        for (ConsumeStrategyConfig strategy : defaultStrategies) {
            strategy.setIsDefault("0");
            consumeStrategyConfigMapper.updateById(strategy);
        }
    }

} 