package org.dromara.member;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 会员模块
 *
 * <AUTHOR>
 */
@SpringBootApplication
public class MemberApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(MemberApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  会员模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
