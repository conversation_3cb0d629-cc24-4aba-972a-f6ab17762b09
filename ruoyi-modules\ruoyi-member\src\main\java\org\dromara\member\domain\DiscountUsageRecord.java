package org.dromara.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 优惠使用记录对象 discount_usage_record
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("discount_usage_record")
public class DiscountUsageRecord extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 使用的规则ID
     */
    private Long ruleId;

    /**
     * 规则名称（冗余字段）
     */
    private String ruleName;

    /**
     * 规则类型
     */
    private String ruleType;

    /**
     * 原始金额
     */
    private BigDecimal originalAmount;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 最终金额
     */
    private BigDecimal finalAmount;

    /**
     * 适用商品信息（JSON格式）
     */
    private String applicableProducts;

    /**
     * 使用时间
     */
    private Date usageTime;

    /**
     * 状态：1-已使用，2-已退款
     */
    private String status;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

}
