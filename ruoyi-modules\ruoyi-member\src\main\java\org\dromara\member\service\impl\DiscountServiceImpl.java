package org.dromara.member.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.member.domain.MemberDiscountStats;
import org.dromara.member.domain.bo.DiscountUsageRecordBo;
import org.dromara.member.domain.dto.DiscountRequest;
import org.dromara.member.domain.dto.DiscountResult;
import org.dromara.member.domain.dto.DiscountUseRequest;
import org.dromara.member.domain.vo.DiscountRuleVo;
import org.dromara.member.domain.vo.DiscountUsageRecordVo;
import org.dromara.member.mapper.MemberDiscountStatsMapper;
import org.dromara.member.service.IDiscountRuleService;
import org.dromara.member.service.IDiscountService;
import org.dromara.member.service.IDiscountUsageRecordService;
import org.dromara.member.service.calculator.DiscountCalculator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 优惠服务实现
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DiscountServiceImpl implements IDiscountService {

    private final IDiscountRuleService discountRuleService;
    private final IDiscountUsageRecordService discountUsageRecordService;
    private final MemberDiscountStatsMapper memberDiscountStatsMapper;

    @Autowired
    private Map<String, DiscountCalculator> calculatorMap;

    private static final String DISCOUNT_LOCK_PREFIX = "discount:lock:";

    /**
     * 计算订单优惠
     */
    @Override
    public DiscountResult calculateDiscount(DiscountRequest request) {
        try {
            log.info("开始计算优惠，会员ID：{}，订单金额：{}", request.getMemberId(), request.getOriginalAmount());

            // 获取适用的优惠规则
            List<DiscountRuleVo> applicableRules = getApplicableRules(request);
            if (CollUtil.isEmpty(applicableRules)) {
                log.info("未找到适用的优惠规则，会员ID：{}", request.getMemberId());
                return DiscountResult.success(request.getOriginalAmount(), BigDecimal.ZERO, 
                    request.getOriginalAmount(), new ArrayList<>());
            }

            // 计算最优优惠方案
            List<DiscountResult.DiscountPlan> plans = calculateDiscountPlans(request, applicableRules);
            if (CollUtil.isEmpty(plans)) {
                return DiscountResult.success(request.getOriginalAmount(), BigDecimal.ZERO, 
                    request.getOriginalAmount(), new ArrayList<>());
            }

            // 选择最优方案（优惠金额最大的）
            DiscountResult.DiscountPlan bestPlan = plans.stream()
                .max(Comparator.comparing(DiscountResult.DiscountPlan::getTotalDiscountAmount))
                .orElse(plans.get(0));

            DiscountResult result = DiscountResult.success(
                request.getOriginalAmount(),
                bestPlan.getTotalDiscountAmount(),
                bestPlan.getFinalAmount(),
                bestPlan.getRules()
            );
            result.setAvailablePlans(plans);

            log.info("优惠计算完成，会员ID：{}，优惠金额：{}", request.getMemberId(), bestPlan.getTotalDiscountAmount());
            return result;

        } catch (Exception e) {
            log.error("优惠计算异常，会员ID：{}，订单金额：{}", request.getMemberId(), request.getOriginalAmount(), e);
            return DiscountResult.failure("优惠计算失败：" + e.getMessage());
        }
    }

    /**
     * 获取适用的优惠规则
     */
    private List<DiscountRuleVo> getApplicableRules(DiscountRequest request) {
        if (CollUtil.isNotEmpty(request.getRuleIds())) {
            // 如果指定了规则ID，只检查这些规则
            return request.getRuleIds().stream()
                .map(discountRuleService::queryById)
                .filter(Objects::nonNull)
                .filter(rule -> discountRuleService.isRuleAvailable(rule.getId(), request.getMemberId()))
                .collect(Collectors.toList());
        } else {
            // 获取所有适用的规则
            return discountRuleService.getApplicableRules(
                request.getOriginalAmount(), 
                request.getMemberId(), 
                request.getIsNewUser()
            );
        }
    }

    /**
     * 计算优惠方案
     */
    private List<DiscountResult.DiscountPlan> calculateDiscountPlans(DiscountRequest request, 
                                                                    List<DiscountRuleVo> rules) {
        List<DiscountResult.DiscountPlan> plans = new ArrayList<>();

        // 单规则方案
        for (DiscountRuleVo rule : rules) {
            DiscountCalculator calculator = getCalculator(rule.getRuleType());
            if (calculator != null && calculator.isApplicable(rule, request)) {
                BigDecimal discountAmount = calculator.calculateDiscount(rule, request);
                if (discountAmount.compareTo(BigDecimal.ZERO) > 0) {
                    DiscountResult.DiscountPlan plan = createSingleRulePlan(rule, request, discountAmount);
                    plans.add(plan);
                }
            }
        }

        // 多规则叠加方案（如果支持叠加）
        List<DiscountRuleVo> stackableRules = rules.stream()
            .filter(rule -> "1".equals(rule.getCanStack()))
            .collect(Collectors.toList());

        if (stackableRules.size() > 1) {
            DiscountResult.DiscountPlan stackedPlan = calculateStackedPlan(request, stackableRules);
            if (stackedPlan != null) {
                plans.add(stackedPlan);
            }
        }

        return plans;
    }

    /**
     * 创建单规则方案
     */
    private DiscountResult.DiscountPlan createSingleRulePlan(DiscountRuleVo rule, DiscountRequest request, 
                                                            BigDecimal discountAmount) {
        DiscountResult.DiscountPlan plan = new DiscountResult.DiscountPlan();
        plan.setPlanName(rule.getRuleName());
        plan.setPlanDescription("使用" + rule.getRuleName() + "优惠");
        plan.setTotalDiscountAmount(discountAmount);
        plan.setFinalAmount(request.getOriginalAmount().subtract(discountAmount));
        plan.setRecommended(false);

        DiscountResult.UsedRule usedRule = new DiscountResult.UsedRule();
        usedRule.setRuleId(rule.getId());
        usedRule.setRuleName(rule.getRuleName());
        usedRule.setRuleType(rule.getRuleType());
        usedRule.setDiscountAmount(discountAmount);

        plan.setRules(Collections.singletonList(usedRule));
        return plan;
    }

    /**
     * 计算叠加方案
     */
    private DiscountResult.DiscountPlan calculateStackedPlan(DiscountRequest request, 
                                                            List<DiscountRuleVo> stackableRules) {
        BigDecimal totalDiscount = BigDecimal.ZERO;
        BigDecimal currentAmount = request.getOriginalAmount();
        List<DiscountResult.UsedRule> usedRules = new ArrayList<>();

        // 按优先级排序
        stackableRules.sort(Comparator.comparing(DiscountRuleVo::getPriority).reversed());

        for (DiscountRuleVo rule : stackableRules) {
            DiscountCalculator calculator = getCalculator(rule.getRuleType());
            if (calculator != null) {
                // 创建临时请求对象，使用当前金额
                DiscountRequest tempRequest = new DiscountRequest();
                tempRequest.setMemberId(request.getMemberId());
                tempRequest.setOriginalAmount(currentAmount);
                tempRequest.setProducts(request.getProducts());
                tempRequest.setIsNewUser(request.getIsNewUser());

                if (calculator.isApplicable(rule, tempRequest)) {
                    BigDecimal discountAmount = calculator.calculateDiscount(rule, tempRequest);
                    if (discountAmount.compareTo(BigDecimal.ZERO) > 0) {
                        totalDiscount = totalDiscount.add(discountAmount);
                        currentAmount = currentAmount.subtract(discountAmount);

                        DiscountResult.UsedRule usedRule = new DiscountResult.UsedRule();
                        usedRule.setRuleId(rule.getId());
                        usedRule.setRuleName(rule.getRuleName());
                        usedRule.setRuleType(rule.getRuleType());
                        usedRule.setDiscountAmount(discountAmount);
                        usedRules.add(usedRule);
                    }
                }
            }
        }

        if (CollUtil.isEmpty(usedRules)) {
            return null;
        }

        DiscountResult.DiscountPlan plan = new DiscountResult.DiscountPlan();
        plan.setPlanName("叠加优惠");
        plan.setPlanDescription("使用多个优惠叠加");
        plan.setTotalDiscountAmount(totalDiscount);
        plan.setFinalAmount(request.getOriginalAmount().subtract(totalDiscount));
        plan.setRules(usedRules);
        plan.setRecommended(true);

        return plan;
    }

    /**
     * 获取计算器
     */
    private DiscountCalculator getCalculator(String ruleType) {
        String calculatorName = getCalculatorName(ruleType);
        return calculatorMap.get(calculatorName);
    }

    /**
     * 获取计算器名称
     */
    private String getCalculatorName(String ruleType) {
        switch (ruleType) {
            case "1":
                return "fullReductionCalculator";
            case "2":
                return "percentageDiscountCalculator";
            case "4":
                return "ladderDiscountCalculator";
            default:
                return null;
        }
    }

    /**
     * 获取最优优惠方案
     */
    @Override
    public List<DiscountResult.DiscountPlan> getBestDiscountPlans(DiscountRequest request) {
        DiscountResult result = calculateDiscount(request);
        return result.getSuccess() ? result.getAvailablePlans() : new ArrayList<>();
    }

    /**
     * 使用优惠
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DiscountResult useDiscount(DiscountUseRequest request) {
        String lockKey = DISCOUNT_LOCK_PREFIX + request.getMemberId() + ":" + request.getOrderNo();

        try {
            // 获取分布式锁
            boolean lockAcquired = RedisUtils.setObjectIfAbsent(lockKey, "1", Duration.ofSeconds(30));
            if (!lockAcquired) {
                return DiscountResult.failure("操作过于频繁，请稍后重试");
            }

            log.info("开始使用优惠，会员ID：{}，订单号：{}", request.getMemberId(), request.getOrderNo());

            // 验证规则可用性
            for (Long ruleId : request.getRuleIds()) {
                if (!discountRuleService.isRuleAvailable(ruleId, request.getMemberId())) {
                    return DiscountResult.failure("优惠规则不可用或已过期");
                }
            }

            // 计算优惠
            DiscountRequest calcRequest = new DiscountRequest();
            calcRequest.setMemberId(request.getMemberId());
            calcRequest.setOriginalAmount(request.getOriginalAmount());
            calcRequest.setRuleIds(request.getRuleIds());
            calcRequest.setProducts(request.getProducts());

            DiscountResult calcResult = calculateDiscount(calcRequest);
            if (!calcResult.getSuccess()) {
                return calcResult;
            }

            // 记录使用明细
            for (DiscountResult.UsedRule usedRule : calcResult.getUsedRules()) {
                DiscountUsageRecordBo recordBo = new DiscountUsageRecordBo();
                recordBo.setMemberId(request.getMemberId());
                recordBo.setOrderNo(request.getOrderNo());
                recordBo.setRuleId(usedRule.getRuleId());
                recordBo.setRuleName(usedRule.getRuleName());
                recordBo.setRuleType(usedRule.getRuleType());
                recordBo.setOriginalAmount(request.getOriginalAmount());
                recordBo.setDiscountAmount(usedRule.getDiscountAmount());
                recordBo.setFinalAmount(calcResult.getFinalAmount());
                recordBo.setStatus("1"); // 已使用
                recordBo.setRemark(request.getRemark());

                discountUsageRecordService.recordUsage(recordBo);

                // 更新规则使用次数
                discountRuleService.incrementUsedCount(usedRule.getRuleId());

                // 更新会员使用统计
                updateMemberUsageStats(request.getMemberId(), usedRule.getRuleId());
            }

            log.info("优惠使用成功，会员ID：{}，订单号：{}，优惠金额：{}",
                request.getMemberId(), request.getOrderNo(), calcResult.getTotalDiscountAmount());

            return calcResult;

        } catch (Exception e) {
            log.error("使用优惠异常，会员ID：{}，订单号：{}", request.getMemberId(), request.getOrderNo(), e);
            return DiscountResult.failure("使用优惠失败：" + e.getMessage());
        } finally {
            // 释放锁
            RedisUtils.deleteObject(lockKey);
        }
    }

    /**
     * 更新会员使用统计
     */
    private void updateMemberUsageStats(Long memberId, Long ruleId) {
        try {
            MemberDiscountStats stats = memberDiscountStatsMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<MemberDiscountStats>()
                    .eq(MemberDiscountStats::getMemberId, memberId)
                    .eq(MemberDiscountStats::getRuleId, ruleId)
            );

            if (stats == null) {
                stats = new MemberDiscountStats();
                stats.setMemberId(memberId);
                stats.setRuleId(ruleId);
                stats.setUsedCount(1);
                stats.setLastUsedTime(new Date());
                stats.setCreateTime(new Date());
                stats.setUpdateTime(new Date());
                memberDiscountStatsMapper.insert(stats);
            } else {
                stats.setUsedCount(stats.getUsedCount() + 1);
                stats.setLastUsedTime(new Date());
                stats.setUpdateTime(new Date());
                memberDiscountStatsMapper.updateById(stats);
            }
        } catch (Exception e) {
            log.error("更新会员使用统计失败，会员ID：{}，规则ID：{}", memberId, ruleId, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 退款处理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processRefund(String orderNo, BigDecimal refundAmount) {
        try {
            log.info("开始处理优惠退款，订单号：{}，退款金额：{}", orderNo, refundAmount);

            // 查询使用记录
            List<DiscountUsageRecordVo> usageRecords = discountUsageRecordService.getByOrderNo(orderNo);
            if (CollUtil.isEmpty(usageRecords)) {
                log.info("订单{}未使用优惠，无需处理", orderNo);
                return true;
            }

            // 更新使用记录状态
            discountUsageRecordService.updateStatusToRefunded(orderNo);

            // 减少规则使用次数
            for (DiscountUsageRecordVo record : usageRecords) {
                discountRuleService.decrementUsedCount(record.getRuleId());

                // 减少会员使用统计
                decrementMemberUsageStats(record.getMemberId(), record.getRuleId());
            }

            log.info("优惠退款处理完成，订单号：{}", orderNo);
            return true;

        } catch (Exception e) {
            log.error("处理优惠退款异常，订单号：{}", orderNo, e);
            throw new RuntimeException("退款处理失败：" + e.getMessage());
        }
    }

    /**
     * 减少会员使用统计
     */
    private void decrementMemberUsageStats(Long memberId, Long ruleId) {
        try {
            MemberDiscountStats stats = memberDiscountStatsMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<MemberDiscountStats>()
                    .eq(MemberDiscountStats::getMemberId, memberId)
                    .eq(MemberDiscountStats::getRuleId, ruleId)
            );

            if (stats != null && stats.getUsedCount() > 0) {
                stats.setUsedCount(stats.getUsedCount() - 1);
                stats.setUpdateTime(new Date());
                memberDiscountStatsMapper.updateById(stats);
            }
        } catch (Exception e) {
            log.error("减少会员使用统计失败，会员ID：{}，规则ID：{}", memberId, ruleId, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 获取会员优惠使用历史
     */
    @Override
    public TableDataInfo<DiscountUsageRecordVo> getMemberUsageHistory(Long memberId, PageQuery pageQuery) {
        return discountUsageRecordService.getMemberUsageHistory(memberId, pageQuery);
    }

    /**
     * 预览优惠效果
     */
    @Override
    public DiscountResult previewDiscount(DiscountRequest request) {
        // 预览不记录使用，直接计算
        return calculateDiscount(request);
    }
}
