package org.dromara.member.controller;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.TimeUnit;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.member.domain.vo.MemberPointsAccountVo;
import org.dromara.member.domain.vo.MemberPointsDetailVo;
import org.dromara.member.domain.bo.MemberPointsAccountBo;
import org.dromara.member.domain.bo.MemberPointsDetailBo;
import org.dromara.member.service.IMemberPointsAccountService;
import org.dromara.member.service.IMemberPointsDetailService;
import org.dromara.member.service.IPointsRuleService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 会员积分管理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/member/points")
public class MemberPointsController extends BaseController {

    private final IMemberPointsAccountService memberPointsAccountService;
    private final IMemberPointsDetailService memberPointsDetailService;
    private final IPointsRuleService pointsRuleService;

    /**
     * 查询会员积分账户列表
     */
    @SaCheckPermission("member:points:list")
    @GetMapping("/account/list")
    public TableDataInfo<MemberPointsAccountVo> accountList(MemberPointsAccountBo bo, PageQuery pageQuery) {
        return memberPointsAccountService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取会员积分账户详细信息
     */
    @SaCheckPermission("member:points:query")
    @GetMapping("/account/{memberId}")
    public R<MemberPointsAccountVo> getAccountInfo(@NotNull(message = "会员ID不能为空")
                                                   @PathVariable Long memberId) {
        MemberPointsAccountVo account = memberPointsAccountService.queryByMemberId(memberId);
        if (account == null) {
            // 如果账户不存在，创建一个新账户
            memberPointsAccountService.createAccount(memberId);
            account = memberPointsAccountService.queryByMemberId(memberId);
        }
        return R.ok(account);
    }

    /**
     * 查询积分明细记录列表
     */
    @SaCheckPermission("member:points:list")
    @GetMapping("/detail/list")
    public TableDataInfo<MemberPointsDetailVo> detailList(MemberPointsDetailBo bo, PageQuery pageQuery) {
        return memberPointsDetailService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取会员积分历史
     */
    @SaCheckPermission("member:points:query")
    @GetMapping("/history/{memberId}")
    public TableDataInfo<MemberPointsDetailVo> getMemberPointsHistory(@NotNull(message = "会员ID不能为空")
                                                                      @PathVariable Long memberId,
                                                                      PageQuery pageQuery) {
        return memberPointsDetailService.getMemberPointsHistory(memberId, pageQuery);
    }

    /**
     * 导出积分明细记录列表
     */
    @SaCheckPermission("member:points:export")
    @Log(title = "积分明细记录", businessType = BusinessType.EXPORT)
    @PostMapping("/detail/export")
    public void exportDetail(MemberPointsDetailBo bo, HttpServletResponse response) {
        List<MemberPointsDetailVo> list = memberPointsDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "积分明细记录", MemberPointsDetailVo.class, response);
    }

    /**
     * 消费获得积分
     */
    @SaCheckPermission("member:points:add")
    @Log(title = "消费获得积分", businessType = BusinessType.INSERT)
    @RepeatSubmit(interval = 2, timeUnit = TimeUnit.SECONDS, message = "请勿重复提交")
    @PostMapping("/consume")
    public R<Void> consumePoints(@RequestParam @NotNull(message = "会员ID不能为空") Long memberId,
                                 @RequestParam @NotNull(message = "消费金额不能为空") @DecimalMin(value = "0.01", message = "消费金额必须大于0") BigDecimal amount,
                                 @RequestParam(required = false) String orderNo) {
        // 根据消费金额计算积分
        Long points = pointsRuleService.calculatePoints(amount);
        if (points <= 0) {
            return R.fail("当前消费金额未达到积分获取条件");
        }

        // 为会员增加积分
        boolean success = memberPointsAccountService.addPoints(memberId, points, orderNo, amount);
        if (success) {
            return R.ok("成功获得 " + points + " 积分");
        } else {
            return R.fail("积分获取失败");
        }
    }

    /**
     * 根据消费金额计算积分
     */
    @SaCheckPermission("member:points:query")
    @GetMapping("/calculate")
    public R<Long> calculatePoints(@RequestParam @NotNull(message = "消费金额不能为空")
                                   @DecimalMin(value = "0.01", message = "消费金额必须大于0") BigDecimal amount) {
        Long points = pointsRuleService.calculatePoints(amount);
        return R.ok(points);
    }

    /**
     * 获取会员积分余额
     */
    @SaCheckPermission("member:points:query")
    @GetMapping("/balance/{memberId}")
    public R<Long> getMemberPointsBalance(@NotNull(message = "会员ID不能为空")
                                          @PathVariable Long memberId) {
        Long balance = memberPointsAccountService.getMemberPoints(memberId);
        return R.ok(balance);
    }

    /**
     * 管理员调整积分
     */
    @SaCheckPermission("member:points:edit")
    @Log(title = "管理员调整积分", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/adjust")
    public R<Void> adjustPoints(@RequestParam @NotNull(message = "会员ID不能为空") Long memberId,
                                @RequestParam @NotNull(message = "积分变动数量不能为空") Long pointsChange,
                                @RequestParam(required = false) String remark) {
        if (pointsChange == 0) {
            return R.fail("积分变动数量不能为0");
        }

        if (pointsChange > 0) {
            // 增加积分
            boolean success = memberPointsAccountService.addPoints(memberId, pointsChange, null, null);
            if (success) {
                return R.ok("积分调整成功");
            } else {
                return R.fail("积分调整失败");
            }
        } else {
            // 减少积分的逻辑需要额外实现
            return R.fail("暂不支持减少积分操作");
        }
    }
}
