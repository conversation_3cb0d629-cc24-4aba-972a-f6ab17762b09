package org.dromara.member.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.member.domain.bo.MemberPointsDetailBo;
import org.dromara.member.domain.vo.MemberPointsDetailVo;
import org.dromara.member.service.IMemberPointsDetailService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 积分明细记录
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/member/pointsDetail")
public class MemberPointsDetailController extends BaseController {

    private final IMemberPointsDetailService memberPointsDetailService;

    /**
     * 查询积分明细记录列表
     */
    @SaCheckPermission("member:pointsDetail:list")
    @GetMapping("/list")
    public TableDataInfo<MemberPointsDetailVo> list(MemberPointsDetailBo bo, PageQuery pageQuery) {
        return memberPointsDetailService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出积分明细记录列表
     */
    @SaCheckPermission("member:pointsDetail:export")
    @Log(title = "积分明细记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MemberPointsDetailBo bo, HttpServletResponse response) {
        List<MemberPointsDetailVo> list = memberPointsDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "积分明细记录", MemberPointsDetailVo.class, response);
    }

    /**
     * 获取积分明细记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("member:pointsDetail:query")
    @GetMapping("/{id}")
    public R<MemberPointsDetailVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(memberPointsDetailService.queryById(id));
    }

    /**
     * 新增积分明细记录
     */
    @SaCheckPermission("member:pointsDetail:add")
    @Log(title = "积分明细记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MemberPointsDetailBo bo) {
        return toAjax(memberPointsDetailService.insertByBo(bo));
    }

    /**
     * 修改积分明细记录
     */
    @SaCheckPermission("member:pointsDetail:edit")
    @Log(title = "积分明细记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MemberPointsDetailBo bo) {
        return toAjax(memberPointsDetailService.updateByBo(bo));
    }

    /**
     * 删除积分明细记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("member:pointsDetail:remove")
    @Log(title = "积分明细记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(memberPointsDetailService.deleteWithValidByIds(List.of(ids), true));
    }
}
