package org.dromara.member.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 钱包操作结果VO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class WalletOperationResultVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 操作金额
     */
    private BigDecimal operationAmount;

    /**
     * 操作前总余额
     */
    private BigDecimal balanceBefore;

    /**
     * 操作后总余额
     */
    private BigDecimal balanceAfter;

    /**
     * 扣除的充值金额
     */
    private BigDecimal rechargeAmountUsed;

    /**
     * 扣除的赠送金额
     */
    private BigDecimal bonusAmountUsed;

    /**
     * 获得的积分（仅消费时有值）
     */
    private Long pointsEarned;

    /**
     * 操作描述
     */
    private String description;

    /**
     * 错误信息（失败时）
     */
    private String errorMessage;

} 