package org.dromara.member.performance;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import org.dromara.member.service.IPointsService;
import org.dromara.member.service.IPointsCacheService;
import org.dromara.member.service.IPointsRuleService;

import java.math.BigDecimal;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 积分性能测试类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@SpringBootTest
@SpringJUnitConfig
@Transactional
public class PointsPerformanceTest {

    @Autowired
    private IPointsService pointsService;

    @Autowired
    private IPointsCacheService pointsCacheService;

    @Autowired
    private IPointsRuleService pointsRuleService;

    /**
     * 测试缓存性能
     */
    @Test
    public void testCachePerformance() {
        Long memberId = 1L;
        
        // 第一次查询（从数据库）
        long start1 = System.currentTimeMillis();
        Long balance1 = pointsService.getMemberPointsBalance(memberId);
        long time1 = System.currentTimeMillis() - start1;
        
        // 第二次查询（从缓存）
        long start2 = System.currentTimeMillis();
        Long balance2 = pointsService.getMemberPointsBalance(memberId);
        long time2 = System.currentTimeMillis() - start2;
        
        System.out.println("第一次查询耗时: " + time1 + "ms");
        System.out.println("第二次查询耗时: " + time2 + "ms");
        
        // 缓存查询应该更快
        assertTrue(time2 <= time1, "缓存查询应该更快");
        assertEquals(balance1, balance2, "查询结果应该一致");
    }

    /**
     * 测试积分规则缓存性能
     */
    @Test
    public void testRulesCachePerformance() {
        // 第一次查询（从数据库）
        long start1 = System.currentTimeMillis();
        var rules1 = pointsRuleService.getActiveRules();
        long time1 = System.currentTimeMillis() - start1;
        
        // 第二次查询（从缓存）
        long start2 = System.currentTimeMillis();
        var rules2 = pointsRuleService.getActiveRules();
        long time2 = System.currentTimeMillis() - start2;
        
        System.out.println("规则第一次查询耗时: " + time1 + "ms");
        System.out.println("规则第二次查询耗时: " + time2 + "ms");
        
        // 缓存查询应该更快
        assertTrue(time2 <= time1, "缓存查询应该更快");
        assertEquals(rules1.size(), rules2.size(), "查询结果应该一致");
    }

    /**
     * 测试并发性能
     */
    @Test
    public void testConcurrentPerformance() throws InterruptedException {
        Long memberId = 100L;
        int threadCount = 10;
        int operationsPerThread = 5;
        
        // 创建积分账户
        pointsService.createMemberPointsAccount(memberId);
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        // 模拟消费获得积分
                        String orderNo = "ORDER_" + threadIndex + "_" + j;
                        pointsService.consumeEarnPoints(memberId, new BigDecimal("10.00"), orderNo);
                        
                        // 查询积分余额
                        pointsService.getMemberPointsBalance(memberId);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        System.out.println("并发测试完成，总耗时: " + totalTime + "ms");
        System.out.println("总操作数: " + (threadCount * operationsPerThread * 2));
        System.out.println("平均每操作耗时: " + (totalTime / (threadCount * operationsPerThread * 2.0)) + "ms");
        
        // 验证最终积分
        Long finalBalance = pointsService.getMemberPointsBalance(memberId);
        System.out.println("最终积分余额: " + finalBalance);
        
        // 每次消费10元应该获得10积分
        Long expectedPoints = (long) (threadCount * operationsPerThread * 10);
        assertEquals(expectedPoints, finalBalance, "积分计算应该正确");
    }

    /**
     * 测试乐观锁性能
     */
    @Test
    public void testOptimisticLockPerformance() throws InterruptedException {
        Long memberId = 200L;
        int threadCount = 5;
        
        // 创建积分账户
        pointsService.createMemberPointsAccount(memberId);
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    // 同时对同一个账户进行积分操作
                    String orderNo = "LOCK_TEST_" + threadIndex;
                    pointsService.consumeEarnPoints(memberId, new BigDecimal("5.00"), orderNo);
                } catch (Exception e) {
                    System.out.println("线程 " + threadIndex + " 执行异常: " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(10, TimeUnit.SECONDS);
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        System.out.println("乐观锁测试完成，总耗时: " + totalTime + "ms");
        
        // 验证最终积分（每次5元获得10积分）
        Long finalBalance = pointsService.getMemberPointsBalance(memberId);
        System.out.println("最终积分余额: " + finalBalance);
        
        Long expectedPoints = (long) (threadCount * 10);
        assertEquals(expectedPoints, finalBalance, "乐观锁应该保证数据一致性");
    }
}
