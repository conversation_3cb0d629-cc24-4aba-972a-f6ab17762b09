package org.dromara.member.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 积分规则服务测试类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@SpringBootTest
@SpringJUnitConfig
@Transactional
public class PointsRuleServiceTest {

    @Autowired
    private IPointsRuleService pointsRuleService;

    /**
     * 测试获取有效积分规则
     */
    @Test
    public void testGetActiveRules() {
        try {
            var rules = pointsRuleService.getActiveRules();
            assertNotNull(rules);
            System.out.println("获取有效积分规则成功，规则数量：" + rules.size());
            
            for (var rule : rules) {
                System.out.println("规则：" + rule.getRuleName() + 
                    ", 最小金额：" + rule.getMinAmount() + 
                    ", 最大金额：" + rule.getMaxAmount() +
                    ", 固定积分：" + rule.getFixedPoints() +
                    ", 积分比例：" + rule.getPointsRatio());
            }
        } catch (Exception e) {
            System.err.println("测试失败：" + e.getMessage());
            e.printStackTrace();
            fail("获取有效积分规则失败：" + e.getMessage());
        }
    }

    /**
     * 测试积分计算
     */
    @Test
    public void testCalculatePoints() {
        try {
            // 测试满2元加3积分
            Long points1 = pointsRuleService.calculatePoints(new BigDecimal("2.00"));
            System.out.println("消费2元获得积分：" + points1);

            // 测试满5元加10积分
            Long points2 = pointsRuleService.calculatePoints(new BigDecimal("5.00"));
            System.out.println("消费5元获得积分：" + points2);

            // 测试满10元按1:1比例送积分
            Long points3 = pointsRuleService.calculatePoints(new BigDecimal("15.00"));
            System.out.println("消费15元获得积分：" + points3);

            // 测试不满足条件
            Long points4 = pointsRuleService.calculatePoints(new BigDecimal("1.00"));
            System.out.println("消费1元获得积分：" + points4);
            
        } catch (Exception e) {
            System.err.println("积分计算测试失败：" + e.getMessage());
            e.printStackTrace();
            fail("积分计算失败：" + e.getMessage());
        }
    }

    /**
     * 测试查找最佳规则
     */
    @Test
    public void testFindBestRule() {
        try {
            var rule1 = pointsRuleService.findBestRule(new BigDecimal("2.50"));
            if (rule1 != null) {
                System.out.println("2.5元匹配规则：" + rule1.getRuleName());
            } else {
                System.out.println("2.5元未匹配到规则");
            }

            var rule2 = pointsRuleService.findBestRule(new BigDecimal("7.50"));
            if (rule2 != null) {
                System.out.println("7.5元匹配规则：" + rule2.getRuleName());
            } else {
                System.out.println("7.5元未匹配到规则");
            }

            var rule3 = pointsRuleService.findBestRule(new BigDecimal("20.00"));
            if (rule3 != null) {
                System.out.println("20元匹配规则：" + rule3.getRuleName());
            } else {
                System.out.println("20元未匹配到规则");
            }
            
        } catch (Exception e) {
            System.err.println("查找最佳规则测试失败：" + e.getMessage());
            e.printStackTrace();
            fail("查找最佳规则失败：" + e.getMessage());
        }
    }
}
