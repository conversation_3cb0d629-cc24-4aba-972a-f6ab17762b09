package org.dromara.member.utils;

import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 会员编号生成器
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Component
public class MemberNoGenerator {

    private static final String PREFIX = "MB";
    private static final AtomicLong SEQUENCE = new AtomicLong(1);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 生成会员编号
     * 格式：MB + 年月日 + 4位递增序号
     * 示例：MB20240101001
     *
     * @return 会员编号
     */
    public String generateMemberNo() {
        String dateStr = LocalDateTime.now().format(DATE_FORMATTER);
        long seq = SEQUENCE.getAndIncrement();
        
        // 如果序号超过9999，重置为1
        if (seq > 9999) {
            SEQUENCE.set(1);
            seq = 1;
        }
        
        return String.format("%s%s%04d", PREFIX, dateStr, seq);
    }

    /**
     * 生成订单号
     * 格式：ORD + 年月日时分秒 + 3位递增序号
     * 示例：ORD20240101120000001
     *
     * @return 订单号
     */
    public String generateOrderNo() {
        String dateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        long seq = SEQUENCE.getAndIncrement();
        
        // 如果序号超过999，重置为1
        if (seq > 999) {
            SEQUENCE.set(1);
            seq = 1;
        }
        
        return String.format("ORD%s%03d", dateTimeStr, seq);
    }
} 