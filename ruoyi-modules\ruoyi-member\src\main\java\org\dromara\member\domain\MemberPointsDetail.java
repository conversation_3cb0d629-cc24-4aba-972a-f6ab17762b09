package org.dromara.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.member.domain.base.SimpleBaseEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 积分明细记录对象 member_points_detail
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("member_points_detail")
public class MemberPointsDetail extends SimpleBaseEntity {

    /**
     * 租户编号
     */
    private String tenantId;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 关联订单号
     */
    private String orderNo;

    /**
     * 业务类型：1-消费获得，2-签到获得，3-活动获得，4-积分过期，5-管理员调整
     */
    private String businessType;

    /**
     * 积分变动数量（正数为增加，负数为减少）
     */
    private Long pointsChange;

    /**
     * 变动前积分余额
     */
    private Long pointsBefore;

    /**
     * 变动后积分余额
     */
    private Long pointsAfter;

    /**
     * 关联规则ID
     */
    private Long ruleId;

    /**
     * 消费金额
     */
    private BigDecimal consumeAmount;

    /**
     * 积分过期时间（可选）
     */
    private Date expireTime;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

}
