package org.dromara.member.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.member.domain.PointsRule;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 积分规则配置视图对象 points_rule
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PointsRule.class)
public class PointsRuleVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 规则名称
     */
    @ExcelProperty(value = "规则名称")
    private String ruleName;

    /**
     * 最小消费金额
     */
    @ExcelProperty(value = "最小消费金额")
    private BigDecimal minAmount;

    /**
     * 最大消费金额（null表示无上限）
     */
    @ExcelProperty(value = "最大消费金额")
    private BigDecimal maxAmount;

    /**
     * 积分比例（每元获得积分数）
     */
    @ExcelProperty(value = "积分比例")
    private BigDecimal pointsRatio;

    /**
     * 固定积分数（优先级高于比例）
     */
    @ExcelProperty(value = "固定积分数")
    private Integer fixedPoints;

    /**
     * 规则生效开始时间
     */
    @ExcelProperty(value = "规则生效开始时间")
    private Date startTime;

    /**
     * 规则生效结束时间
     */
    @ExcelProperty(value = "规则生效结束时间")
    private Date endTime;

    /**
     * 状态：0-启用，1-禁用
     */
    @ExcelProperty(value = "状态", converter = org.dromara.common.excel.convert.ExcelDictConvert.class)
    @org.dromara.common.excel.annotation.ExcelDictFormat(dictType = "sys_normal_disable")
    private String status;

    /**
     * 优先级（数字越大优先级越高）
     */
    @ExcelProperty(value = "优先级")
    private Integer priority;

    /**
     * 备注说明
     */
    @ExcelProperty(value = "备注说明")
    private String remark;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

}
