package org.dromara.member.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.member.domain.PointsRule;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



/**
 * 积分规则配置视图对象 points_rule
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PointsRule.class)
public class PointsRuleVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 规则名称
     */
    @ExcelProperty(value = "规则名称")
    private String ruleName;

    /**
     * 最小消费金额
     */
    @ExcelProperty(value = "最小消费金额")
    private BigDecimal minAmount;

    /**
     * 最大消费金额（null表示无上限）
     */
    @ExcelProperty(value = "最大消费金额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "n=ull表示无上限")
    private BigDecimal maxAmount;

    /**
     * 积分比例（每元获得积分数）
     */
    @ExcelProperty(value = "积分比例", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "每=元获得积分数")
    private BigDecimal pointsRatio;

    /**
     * 固定积分数（优先级高于比例）
     */
    @ExcelProperty(value = "固定积分数", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "优=先级高于比例")
    private BigDecimal fixedPoints;

    /**
     * 规则生效开始时间
     */
    @ExcelProperty(value = "规则生效开始时间")
    private Date startTime;

    /**
     * 规则生效结束时间
     */
    @ExcelProperty(value = "规则生效结束时间")
    private Date endTime;

    /**
     * 状态：0-禁用，1-启用
     */
    @ExcelProperty(value = "状态：0-禁用，1-启用")
    private Long status;

    /**
     * 优先级（数字越大优先级越高）
     */
    @ExcelProperty(value = "优先级", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "数=字越大优先级越高")
    private Long priority;

    /**
     * 备注说明
     */
    @ExcelProperty(value = "备注说明")
    private String remark;

    /**
     * 创建者
     */
    @ExcelProperty(value = "创建者")
    private Long createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新者
     */
    @ExcelProperty(value = "更新者")
    private Long updateBy;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;


}
