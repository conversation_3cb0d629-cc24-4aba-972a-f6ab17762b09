# 充值储值功能完整实现方案

## 已完成的基础结构

### 1. 数据库表结构 ✅
- `member_info` - 会员基础信息表
- `member_statistics` - 会员统计表  
- `member_wallet_account` - 会员钱包账户表
- `member_wallet_detail` - 钱包流水记录表
- `recharge_activity_config` - 充值活动配置表
- `consume_strategy_config` - 消费策略配置表
- `refund_rule_config` - 退款规则配置表

### 2. 枚举类 ✅
- `WalletBusinessType` - 钱包业务类型枚举
- `WalletAmountType` - 钱包金额类型枚举
- `ConsumeStrategyType` - 消费策略类型枚举
- `RefundType` - 退款类型枚举

### 3. 实体类 ✅
- `MemberInfo` - 会员信息实体
- `MemberStatistics` - 会员统计实体
- `MemberWalletAccount` - 钱包账户实体
- `MemberWalletDetail` - 钱包流水实体
- `RechargeActivityConfig` - 充值活动配置实体
- `ConsumeStrategyConfig` - 消费策略配置实体
- `RefundRuleConfig` - 退款规则配置实体

### 4. DTO对象 ✅
- `RechargeRequestDto` - 充值请求DTO
- `ConsumeRequestDto` - 消费请求DTO
- `RefundRequestDto` - 退款请求DTO

### 5. VO对象 ✅
- `WalletBalanceVo` - 钱包余额VO
- `RechargeResultVo` - 充值结果VO
- `WalletOperationResultVo` - 钱包操作结果VO

### 6. 工具类 ✅
- `MemberNoGenerator` - 会员编号生成器

### 7. Service接口 ✅
- `IMemberService` - 会员服务接口
- `IWalletService` - 钱包服务接口

### 8. Controller层 ✅
- `MemberController` - 会员管理Controller
- `WalletController` - 钱包管理Controller
- `RechargeActivityController` - 充值活动配置Controller
- `ConsumeStrategyController` - 消费策略配置Controller
- `RefundRuleController` - 退款规则配置Controller
- `WalletDetailController` - 钱包流水记录Controller

## 待实现的核心组件

### 1. Mapper接口层
```java
// 需要创建的Mapper接口
- MemberInfoMapper
- MemberStatisticsMapper  
- MemberWalletAccountMapper
- MemberWalletDetailMapper
- RechargeActivityConfigMapper
- ConsumeStrategyConfigMapper
- RefundRuleConfigMapper
```

### 2. Service实现层
```java
// 核心业务逻辑实现
- MemberServiceImpl
- WalletServiceImpl
- RechargeActivityService
- ConsumeStrategyService  
- RefundRuleService
```

### 3. Controller控制层
```java
// API接口层
- MemberController
- WalletController
- RechargeController
- ConsumeController
- RefundController
```

## 核心业务流程实现

### 1. 会员注册流程
```java
@Transactional
public Boolean createMember(MemberInfo memberInfo) {
    // 1. 生成会员编号
    // 2. 创建会员基础信息
    // 3. 初始化会员统计记录
    // 4. 创建积分账户
    // 5. 创建钱包账户
    // 6. 发送注册奖励（可选）
}
```

### 2. 充值业务流程
```java
@Transactional
public RechargeResultVo recharge(RechargeRequestDto request) {
    // 1. 验证会员状态
    // 2. 匹配充值活动规则
    // 3. 计算赠送金额
    // 4. 调用支付接口
    // 5. 支付成功后更新钱包账户
    // 6. 记录流水
    // 7. 更新会员统计
}
```

### 3. 消费业务流程  
```java
@Transactional
public WalletOperationResultVo consume(ConsumeRequestDto request) {
    // 1. 验证会员状态和余额
    // 2. 获取消费策略
    // 3. 计算扣款分配（充值vs赠送）
    // 4. 执行钱包扣款
    // 5. 计算获得积分
    // 6. 更新积分账户
    // 7. 记录流水
    // 8. 更新会员统计
}
```

### 4. 退款业务流程
```java
@Transactional  
public WalletOperationResultVo refund(RefundRequestDto request) {
    // 1. 验证会员和订单状态
    // 2. 获取退款规则
    // 3. 计算退款金额分配
    // 4. 退还到钱包账户
    // 5. 扣除相应积分
    // 6. 记录流水
    // 7. 更新会员统计
}
```

## 关键技术实现点

### 1. 消费策略算法
```java
public class ConsumeStrategyCalculator {
    
    /**
     * 策略1：优先扣充值金额
     */
    public ConsumeAllocation calculateRechargeFirst(BigDecimal amount, WalletAccount account) {
        // 实现逻辑
    }
    
    /**
     * 策略2：优先扣赠送金额  
     */
    public ConsumeAllocation calculateBonusFirst(BigDecimal amount, WalletAccount account) {
        // 实现逻辑
    }
    
    /**
     * 策略3：按比例扣除
     */
    public ConsumeAllocation calculateRatio(BigDecimal amount, WalletAccount account, 
                                          BigDecimal rechargeRatio, BigDecimal bonusRatio) {
        // 实现逻辑
    }
}
```

### 2. 退款策略算法
```java
public class RefundStrategyCalculator {
    
    /**
     * 按比例退款算法
     */
    public RefundAllocation calculateRatioRefund(RefundRequestDto request, 
                                               List<WalletDetail> originalRecords) {
        // 分析原消费记录的金额构成
        // 按比例计算退款分配
    }
}
```

### 3. 并发控制
```java
@Component
public class WalletConcurrencyManager {
    
    /**
     * 乐观锁更新钱包余额
     */
    @Retryable(value = OptimisticLockException.class, maxAttempts = 3)
    public boolean updateWalletWithOptimisticLock(WalletAccount account) {
        // 乐观锁重试机制
    }
}
```

## API接口设计

### 1. 会员相关接口
```java
@RestController
@RequestMapping("/member")
public class MemberController {
    
    @PostMapping("/register") 
    public R<MemberInfo> register(@RequestBody MemberInfo memberInfo);
    
    @GetMapping("/{memberId}")
    public R<MemberInfo> getMember(@PathVariable Long memberId);
    
    @GetMapping("/{memberId}/wallet/balance")
    public R<WalletBalanceVo> getWalletBalance(@PathVariable Long memberId);
}
```

### 2. 钱包相关接口
```java
@RestController
@RequestMapping("/wallet")  
public class WalletController {
    
    @PostMapping("/recharge")
    public R<RechargeResultVo> recharge(@RequestBody RechargeRequestDto request);
    
    @PostMapping("/consume")
    public R<WalletOperationResultVo> consume(@RequestBody ConsumeRequestDto request);
    
    @PostMapping("/refund")
    public R<WalletOperationResultVo> refund(@RequestBody RefundRequestDto request);
}
```

## 配置管理接口

### 1. 充值活动配置
```java
@RestController
@RequestMapping("/config/recharge-activity")
public class RechargeActivityController {
    // CRUD操作
}
```

### 2. 消费策略配置
```java
@RestController
@RequestMapping("/config/consume-strategy")
public class ConsumeStrategyController {
    // CRUD操作
}
```

### 3. 退款规则配置  
```java
@RestController
@RequestMapping("/config/refund-rule")
public class RefundRuleController {
    // CRUD操作
}
```

## 部署和测试

### 1. 数据库初始化
1. 执行 `script/sql/member_wallet.sql` 创建表结构
2. 插入默认配置数据

### 2. 功能测试用例
1. 会员注册测试
2. 充值功能测试（各种活动规则）
3. 消费功能测试（各种策略）
4. 退款功能测试（各种规则）
5. 并发安全测试

### 3. 性能优化
1. 数据库索引优化
2. 缓存策略（活动规则、策略配置）
3. 异步处理（统计数据更新）
4. 分布式锁（高并发场景）

## 扩展功能

### 1. 钱包转账功能
- 会员间转账
- 手续费计算
- 转账限额控制

### 2. 营销功能
- 充值返现活动
- 消费满减优惠
- 会员专享折扣

### 3. 风控功能
- 异常交易监控
- 频繁操作限制
- 风险账户管理

### 4. 报表统计
- 会员消费分析
- 充值趋势统计
- 活动效果分析

## 总结

这个方案提供了一个完整的充值储值功能实现框架，包含：

1. **完整的数据模型**：支持资金分离、流水追踪、配置管理
2. **灵活的业务策略**：支持多种消费和退款策略
3. **安全的并发控制**：乐观锁+重试机制保证数据一致性
4. **丰富的配置选项**：便于运营活动管理
5. **完善的扩展能力**：支持未来业务发展需求

接下来可以按照这个方案逐步实现各个组件，建议按照以下顺序：
1. Mapper接口和XML文件
2. Service实现类
3. Controller接口
4. 单元测试和集成测试
5. 性能优化和安全加固 