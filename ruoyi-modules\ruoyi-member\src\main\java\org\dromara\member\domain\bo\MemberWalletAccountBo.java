package org.dromara.member.domain.bo;

import org.dromara.member.domain.MemberWalletAccount;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会员钱包账户业务对象 member_wallet_account
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MemberWalletAccount.class, reverseConvertGenerate = false)
public class MemberWalletAccountBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 总余额
     */
    private Long totalBalance;

    /**
     * 充值余额（用户实际付费）
     */
    private Long rechargeBalance;

    /**
     * 赠送余额（平台赠送）
     */
    private Long bonusBalance;

    /**
     * 冻结余额
     */
    private Long frozenBalance;


}
