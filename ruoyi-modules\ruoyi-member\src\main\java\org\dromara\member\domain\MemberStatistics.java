package org.dromara.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.member.domain.base.SimpleBaseEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员统计对象 member_statistics
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("member_statistics")
public class MemberStatistics extends SimpleBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 累计消费金额
     */
    private BigDecimal totalConsumeAmount;

    /**
     * 累计消费次数
     */
    private Integer totalConsumeCount;

    /**
     * 累计充值金额
     */
    private BigDecimal totalRechargeAmount;

    /**
     * 累计充值次数
     */
    private Integer totalRechargeCount;

    /**
     * 最后消费时间
     */
    private Date lastConsumeTime;

    /**
     * 最后充值时间
     */
    private Date lastRechargeTime;

    /**
     * 乐观锁版本号
     */
    @Version
    private Integer version;

} 