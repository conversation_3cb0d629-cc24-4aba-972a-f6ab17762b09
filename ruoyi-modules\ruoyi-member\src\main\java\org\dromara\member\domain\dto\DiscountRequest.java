package org.dromara.member.domain.dto;

import lombok.Data;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 优惠计算请求对象
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class DiscountRequest {

    /**
     * 会员ID
     */
    @NotNull(message = "会员ID不能为空")
    private Long memberId;

    /**
     * 订单号（可选，用于使用优惠时）
     */
    private String orderNo;

    /**
     * 原始金额
     */
    @NotNull(message = "原始金额不能为空")
    @DecimalMin(value = "0.01", message = "原始金额必须大于0")
    private BigDecimal originalAmount;

    /**
     * 商品信息列表
     */
    private List<ProductInfo> products;

    /**
     * 指定使用的规则ID列表（可选）
     */
    private List<Long> ruleIds;

    /**
     * 是否新用户
     */
    private Boolean isNewUser;

    /**
     * 商品信息
     */
    @Data
    public static class ProductInfo {
        /**
         * 商品ID
         */
        private Long productId;

        /**
         * 商品名称
         */
        private String productName;

        /**
         * 商品分类ID
         */
        private Long categoryId;

        /**
         * 商品价格
         */
        private BigDecimal price;

        /**
         * 商品数量
         */
        private Integer quantity;

        /**
         * 商品总金额
         */
        private BigDecimal totalAmount;
    }

}
