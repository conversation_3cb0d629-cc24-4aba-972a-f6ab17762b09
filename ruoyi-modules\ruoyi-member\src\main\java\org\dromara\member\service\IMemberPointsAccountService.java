package org.dromara.member.service;

import org.dromara.member.domain.vo.MemberPointsAccountVo;
import org.dromara.member.domain.bo.MemberPointsAccountBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 会员积分账户Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IMemberPointsAccountService {

    /**
     * 查询会员积分账户
     */
    MemberPointsAccountVo queryById(Long id);

    /**
     * 根据会员ID查询积分账户
     */
    MemberPointsAccountVo queryByMemberId(Long memberId);

    /**
     * 查询会员积分账户列表
     */
    TableDataInfo<MemberPointsAccountVo> queryPageList(MemberPointsAccountBo bo, PageQuery pageQuery);

    /**
     * 查询会员积分账户列表
     */
    List<MemberPointsAccountVo> queryList(MemberPointsAccountBo bo);

    /**
     * 新增会员积分账户
     */
    Boolean insertByBo(MemberPointsAccountBo bo);

    /**
     * 修改会员积分账户
     */
    Boolean updateByBo(MemberPointsAccountBo bo);

    /**
     * 校验并批量删除会员积分账户信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 为会员增加积分
     */
    Boolean addPoints(Long memberId, Long points, String orderNo, BigDecimal amount);

    /**
     * 创建积分账户
     */
    Boolean createAccount(Long memberId);

    /**
     * 获取会员积分余额
     */
    Long getMemberPoints(Long memberId);
}
