# 积分系统字段不匹配问题解决方案

## 问题描述

积分规则缓存刷新时出现数据库字段不匹配错误：
```
org.springframework.jdbc.BadSqlGrammarException: Unknown column 'create_dept' in 'field list'
```

## 问题根因

代码中的实体类继承了 `BaseEntity`，该基类包含了以下字段：
- `create_dept` (创建部门)
- 数据库表中缺少这些字段

实际数据库表结构与代码期望的字段不匹配。

## 解决方案（已实施）

### 方案1：创建自定义基类
1. **创建了 `SimpleBaseEntity` 类**
   - 位置：`ruoyi-modules/ruoyi-member/src/main/java/org/dromara/member/domain/base/SimpleBaseEntity.java`
   - 只包含数据库中实际存在的字段
   - 字段类型匹配数据库定义

2. **修改积分相关实体类**
   - `PointsRule` 
   - `MemberPointsAccount`
   - `MemberPointsDetail`
   - 改为继承 `SimpleBaseEntity` 而不是 `BaseEntity`

### 方案2：自定义查询方法
1. **在 `PointsRuleMapper` 中添加自定义查询方法**
   - 使用 `@Select` 注解明确指定查询字段
   - 避免查询数据库中不存在的字段

2. **修改 `PointsRuleServiceImpl` 的 `getActiveRules()` 方法**
   - 使用自定义查询方法替代原来的 LambdaQueryWrapper

## 主要修改内容

### 1. SimpleBaseEntity 类
```java
public class SimpleBaseEntity implements Serializable {
    private String createBy;        // varchar(64) - 匹配数据库类型
    private Date createTime;
    private String updateBy;        // varchar(64) - 匹配数据库类型  
    private Date updateTime;
    // 不包含 create_dept, del_flag, tenant_id 等数据库中不存在的字段
}
```

### 2. PointsRuleMapper 自定义查询
```java
@Select("SELECT id, rule_name, min_amount, max_amount, points_ratio, fixed_points, " +
        "start_time, end_time, status, priority, remark, " +
        "create_by, create_time, update_by, update_time " +
        "FROM points_rule " +
        "WHERE status = #{status} " +
        "AND (start_time IS NULL OR start_time <= #{currentTime}) " +
        "AND (end_time IS NULL OR end_time >= #{currentTime}) " +
        "ORDER BY priority DESC")
List<PointsRuleVo> selectActiveRules(String status, Date currentTime);
```

## 其他可选方案

如果不想修改实体类，还可以考虑：

### 方案3：使用 @TableField(exist = false) 注解
```java
@TableField(exist = false)
private Long createDept; // 标记为数据库中不存在的字段
```

### 方案4：修改数据库表结构（不推荐）
执行 SQL 添加缺失字段：
```sql
ALTER TABLE points_rule ADD COLUMN create_dept bigint(20) DEFAULT NULL COMMENT '创建部门';
```

## 测试验证

修改完成后，请测试以下功能：
1. 积分规则列表查询
2. 积分缓存刷新功能
3. 积分规则的增删改操作

## 注意事项

1. **字段类型匹配**：确保实体类字段类型与数据库字段类型一致
2. **缓存一致性**：修改后需要清理相关缓存
3. **其他模块影响**：检查是否有其他模块依赖修改的实体类

## 验证命令

可以通过以下方式验证修复效果：
```bash
# 检查积分规则缓存刷新
curl -X POST http://localhost:8080/member/pointsRule/cache/refresh

# 查询有效积分规则
curl -X GET http://localhost:8080/member/pointsRule/active
``` 