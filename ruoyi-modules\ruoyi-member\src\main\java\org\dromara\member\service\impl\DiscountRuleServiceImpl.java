package org.dromara.member.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.member.domain.DiscountRule;
import org.dromara.member.domain.MemberDiscountStats;
import org.dromara.member.domain.bo.DiscountRuleBo;
import org.dromara.member.domain.vo.DiscountRuleVo;
import org.dromara.member.mapper.DiscountRuleMapper;
import org.dromara.member.mapper.MemberDiscountStatsMapper;
import org.dromara.member.service.IDiscountCacheService;
import org.dromara.member.service.IDiscountRuleService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 优惠规则配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DiscountRuleServiceImpl implements IDiscountRuleService {

    private final DiscountRuleMapper baseMapper;
    private final MemberDiscountStatsMapper memberDiscountStatsMapper;
    private final IDiscountCacheService discountCacheService;

    /**
     * 查询优惠规则配置
     */
    @Override
    public DiscountRuleVo queryById(Long id) {
        // 先从缓存获取
        DiscountRuleVo cached = discountCacheService.getCachedRuleDetail(id);
        if (ObjectUtil.isNotNull(cached)) {
            return cached;
        }
        
        // 缓存未命中，从数据库查询
        DiscountRuleVo rule = baseMapper.selectVoById(id);
        if (ObjectUtil.isNotNull(rule)) {
            discountCacheService.cacheRuleDetail(id, rule);
        }
        return rule;
    }

    /**
     * 查询优惠规则配置列表
     */
    @Override
    public TableDataInfo<DiscountRuleVo> queryPageList(DiscountRuleBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<DiscountRule> lqw = buildQueryWrapper(bo);
        Page<DiscountRuleVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询优惠规则配置列表
     */
    @Override
    public List<DiscountRuleVo> queryList(DiscountRuleBo bo) {
        LambdaQueryWrapper<DiscountRule> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<DiscountRule> buildQueryWrapper(DiscountRuleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DiscountRule> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getRuleName()), DiscountRule::getRuleName, bo.getRuleName());
        lqw.eq(StringUtils.isNotBlank(bo.getRuleType()), DiscountRule::getRuleType, bo.getRuleType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), DiscountRule::getStatus, bo.getStatus());
        lqw.ge(bo.getMinAmount() != null, DiscountRule::getMinAmount, bo.getMinAmount());
        lqw.le(bo.getMaxAmount() != null, DiscountRule::getMaxAmount, bo.getMaxAmount());
        lqw.between(params.get("beginStartTime") != null && params.get("endStartTime") != null,
            DiscountRule::getStartTime, params.get("beginStartTime"), params.get("endStartTime"));
        lqw.between(params.get("beginEndTime") != null && params.get("endEndTime") != null,
            DiscountRule::getEndTime, params.get("beginEndTime"), params.get("endEndTime"));
        lqw.orderByDesc(DiscountRule::getPriority).orderByDesc(DiscountRule::getCreateTime);
        return lqw;
    }

    /**
     * 新增优惠规则配置
     */
    @Override
    public Boolean insertByBo(DiscountRuleBo bo) {
        DiscountRule add = MapstructUtils.convert(bo, DiscountRule.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            // 清除缓存
            discountCacheService.clearRulesCache();
        }
        return flag;
    }

    /**
     * 修改优惠规则配置
     */
    @Override
    public Boolean updateByBo(DiscountRuleBo bo) {
        DiscountRule update = MapstructUtils.convert(bo, DiscountRule.class);
        validEntityBeforeSave(update);
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            // 清除缓存
            discountCacheService.clearRulesCache();
            discountCacheService.clearRuleDetailCache(bo.getId());
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(DiscountRule entity) {
        // 校验金额范围
        if (entity.getMaxAmount() != null && 
            entity.getMinAmount().compareTo(entity.getMaxAmount()) > 0) {
            throw new RuntimeException("最小金额不能大于最大金额");
        }
        
        // 校验时间范围
        if (entity.getStartTime() != null && entity.getEndTime() != null &&
            entity.getStartTime().after(entity.getEndTime())) {
            throw new RuntimeException("开始时间不能晚于结束时间");
        }
        
        // 校验折扣比例
        if ("2".equals(entity.getRuleType()) && 
            (entity.getDiscountValue().compareTo(BigDecimal.ZERO) <= 0 || 
             entity.getDiscountValue().compareTo(BigDecimal.ONE) >= 0)) {
            throw new RuntimeException("折扣比例必须在0-1之间");
        }
    }

    /**
     * 批量删除优惠规则配置
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 检查是否有正在使用的规则
            for (Long id : ids) {
                DiscountRuleVo rule = queryById(id);
                if (rule != null && rule.getUsedCount() > 0) {
                    throw new RuntimeException("规则[" + rule.getRuleName() + "]已被使用，不能删除");
                }
            }
        }
        
        boolean flag = baseMapper.deleteBatchIds(ids) > 0;
        if (flag) {
            // 清除缓存
            discountCacheService.clearRulesCache();
            ids.forEach(discountCacheService::clearRuleDetailCache);
        }
        return flag;
    }

    /**
     * 获取有效的优惠规则列表
     */
    @Override
    public List<DiscountRuleVo> getActiveRules() {
        // 先从缓存获取
        List<DiscountRuleVo> cachedRules = discountCacheService.getCachedActiveRules();
        if (CollUtil.isNotEmpty(cachedRules)) {
            return cachedRules;
        }
        
        // 缓存未命中，从数据库查询
        LambdaQueryWrapper<DiscountRule> lqw = Wrappers.lambdaQuery();
        lqw.eq(DiscountRule::getStatus, "0"); // 启用状态
        Date now = new Date();
        lqw.and(wrapper -> wrapper
            .isNull(DiscountRule::getStartTime)
            .or()
            .le(DiscountRule::getStartTime, now)
        );
        lqw.and(wrapper -> wrapper
            .isNull(DiscountRule::getEndTime)
            .or()
            .ge(DiscountRule::getEndTime, now)
        );
        lqw.orderByDesc(DiscountRule::getPriority);
        List<DiscountRuleVo> rules = baseMapper.selectVoList(lqw);
        
        // 缓存查询结果
        if (CollUtil.isNotEmpty(rules)) {
            discountCacheService.cacheActiveRules(rules);
        }
        
        return rules;
    }

    /**
     * 根据金额获取适用的优惠规则
     */
    @Override
    public List<DiscountRuleVo> getApplicableRules(BigDecimal amount, Long memberId, Boolean isNewUser) {
        List<DiscountRuleVo> activeRules = getActiveRules();
        if (CollUtil.isEmpty(activeRules)) {
            return new ArrayList<>();
        }

        return activeRules.stream()
            .filter(rule -> isRuleApplicableForMember(rule, amount, memberId, isNewUser))
            .collect(Collectors.toList());
    }

    /**
     * 检查规则对会员是否适用
     */
    private boolean isRuleApplicableForMember(DiscountRuleVo rule, BigDecimal amount, Long memberId, Boolean isNewUser) {
        // 检查金额范围
        if (amount.compareTo(rule.getMinAmount()) < 0) {
            return false;
        }
        if (rule.getMaxAmount() != null && amount.compareTo(rule.getMaxAmount()) > 0) {
            return false;
        }

        // 检查用户限制
        if ("2".equals(rule.getUserLimitType()) && !Boolean.TRUE.equals(isNewUser)) {
            return false; // 新用户专享，但不是新用户
        }

        // 检查使用次数限制
        if ("1".equals(rule.getUserLimitType()) && rule.getUserLimitCount() != null) {
            Integer usedCount = getMemberRuleUsedCount(memberId, rule.getId());
            if (usedCount >= rule.getUserLimitCount()) {
                return false;
            }
        }

        // 检查总使用次数限制
        if (rule.getTotalLimitCount() != null && rule.getUsedCount() >= rule.getTotalLimitCount()) {
            return false;
        }

        return true;
    }

    /**
     * 获取会员对规则的使用次数
     */
    private Integer getMemberRuleUsedCount(Long memberId, Long ruleId) {
        LambdaQueryWrapper<MemberDiscountStats> lqw = Wrappers.lambdaQuery();
        lqw.eq(MemberDiscountStats::getMemberId, memberId);
        lqw.eq(MemberDiscountStats::getRuleId, ruleId);
        MemberDiscountStats stats = memberDiscountStatsMapper.selectOne(lqw);
        return stats != null ? stats.getUsedCount() : 0;
    }

    /**
     * 检查规则是否可用
     */
    @Override
    public boolean isRuleAvailable(Long ruleId, Long memberId) {
        DiscountRuleVo rule = queryById(ruleId);
        if (rule == null || !"0".equals(rule.getStatus())) {
            return false;
        }

        // 检查时间范围
        Date now = new Date();
        if (rule.getStartTime() != null && now.before(rule.getStartTime())) {
            return false;
        }
        if (rule.getEndTime() != null && now.after(rule.getEndTime())) {
            return false;
        }

        // 检查使用次数限制
        if ("1".equals(rule.getUserLimitType()) && rule.getUserLimitCount() != null) {
            Integer usedCount = getMemberRuleUsedCount(memberId, ruleId);
            if (usedCount >= rule.getUserLimitCount()) {
                return false;
            }
        }

        // 检查总使用次数限制
        if (rule.getTotalLimitCount() != null && rule.getUsedCount() >= rule.getTotalLimitCount()) {
            return false;
        }

        return true;
    }

    /**
     * 增加规则使用次数
     */
    @Override
    public Boolean incrementUsedCount(Long ruleId) {
        LambdaUpdateWrapper<DiscountRule> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(DiscountRule::getId, ruleId);
        updateWrapper.setSql("used_count = used_count + 1");

        boolean flag = baseMapper.update(null, updateWrapper) > 0;
        if (flag) {
            // 清除缓存
            discountCacheService.clearRuleDetailCache(ruleId);
            discountCacheService.clearRulesCache();
        }
        return flag;
    }

    /**
     * 减少规则使用次数（退款时使用）
     */
    @Override
    public Boolean decrementUsedCount(Long ruleId) {
        LambdaUpdateWrapper<DiscountRule> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(DiscountRule::getId, ruleId);
        updateWrapper.setSql("used_count = GREATEST(used_count - 1, 0)");

        boolean flag = baseMapper.update(null, updateWrapper) > 0;
        if (flag) {
            // 清除缓存
            discountCacheService.clearRuleDetailCache(ruleId);
            discountCacheService.clearRulesCache();
        }
        return flag;
    }
}
