package org.dromara.member.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.member.domain.dto.ConsumeRequestDto;
import org.dromara.member.domain.dto.RechargeRequestDto;
import org.dromara.member.domain.dto.RefundRequestDto;
import org.dromara.member.domain.vo.RechargeResultVo;
import org.dromara.member.domain.vo.WalletBalanceVo;
import org.dromara.member.domain.vo.WalletOperationResultVo;
import org.dromara.member.service.IWalletService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 钱包管理Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/wallet")
public class WalletController extends BaseController {

    private final IWalletService walletService;

    /**
     * 获取钱包余额
     */
    @GetMapping("/{memberId}/balance")
    public R<WalletBalanceVo> getWalletBalance(@NotNull(message = "会员ID不能为空") @PathVariable Long memberId) {
        WalletBalanceVo balance = walletService.getWalletBalance(memberId);
        return R.ok(balance);
    }

    /**
     * 充值
     */
    @Log(title = "钱包充值", businessType = BusinessType.INSERT)
    @PostMapping("/recharge")
    public R<RechargeResultVo> recharge(@Validated @RequestBody RechargeRequestDto request) {
        RechargeResultVo result = walletService.recharge(request);
        return R.ok(result);
    }

    /**
     * 消费
     */
    @Log(title = "钱包消费", businessType = BusinessType.UPDATE)
    @PostMapping("/consume")
    public R<WalletOperationResultVo> consume(@Validated @RequestBody ConsumeRequestDto request) {
        WalletOperationResultVo result = walletService.consume(request);
        if (result.getSuccess()) {
            return R.ok(result);
        }
        return R.fail(result.getErrorMessage());
    }

    /**
     * 退款
     */
    @Log(title = "钱包退款", businessType = BusinessType.UPDATE)
    @PostMapping("/refund")
    public R<WalletOperationResultVo> refund(@Validated @RequestBody RefundRequestDto request) {
        WalletOperationResultVo result = walletService.refund(request);
        if (result.getSuccess()) {
            return R.ok(result);
        }
        return R.fail(result.getErrorMessage());
    }

    /**
     * 冻结余额
     */
    @Log(title = "冻结余额", businessType = BusinessType.UPDATE)
    @PostMapping("/{memberId}/freeze")
    public R<Void> freezeBalance(@NotNull(message = "会员ID不能为空") @PathVariable Long memberId,
                                 @DecimalMin(value = "0.01", message = "冻结金额必须大于0") @RequestParam BigDecimal amount,
                                 @RequestParam String reason) {
        boolean result = walletService.freezeBalance(memberId, amount, reason);
        return toAjax(result);
    }

    /**
     * 解冻余额
     */
    @Log(title = "解冻余额", businessType = BusinessType.UPDATE)
    @PostMapping("/{memberId}/unfreeze")
    public R<Void> unfreezeBalance(@NotNull(message = "会员ID不能为空") @PathVariable Long memberId,
                                   @DecimalMin(value = "0.01", message = "解冻金额必须大于0") @RequestParam BigDecimal amount,
                                   @RequestParam String reason) {
        boolean result = walletService.unfreezeBalance(memberId, amount, reason);
        return toAjax(result);
    }

    /**
     * 检查余额是否充足
     */
    @GetMapping("/{memberId}/check-balance")
    public R<Boolean> checkSufficientBalance(@NotNull(message = "会员ID不能为空") @PathVariable Long memberId,
                                             @DecimalMin(value = "0.01", message = "检查金额必须大于0") @RequestParam BigDecimal amount) {
        Boolean sufficient = walletService.checkSufficientBalance(memberId, amount);
        return R.ok(sufficient);
    }

    /**
     * 创建钱包账户
     */
    @Log(title = "创建钱包账户", businessType = BusinessType.INSERT)
    @PostMapping("/{memberId}/create")
    public R<Void> createWalletAccount(@NotNull(message = "会员ID不能为空") @PathVariable Long memberId) {
        boolean result = walletService.createWalletAccount(memberId);
        return toAjax(result);
    }

} 