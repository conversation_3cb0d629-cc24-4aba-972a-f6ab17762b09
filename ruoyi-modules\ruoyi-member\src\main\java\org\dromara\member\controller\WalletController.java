package org.dromara.member.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.member.domain.bo.MemberWalletAccountBo;
import org.dromara.member.domain.dto.ConsumeRequestDto;
import org.dromara.member.domain.dto.RechargeRequestDto;
import org.dromara.member.domain.vo.MemberWalletAccountVo;
import org.dromara.member.domain.vo.RechargeResultVo;
import org.dromara.member.domain.vo.WalletOperationResultVo;
import org.dromara.member.service.IWalletService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 钱包管理Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/member/walletAccount")
public class WalletController extends BaseController {

    private final IWalletService walletService;


    /**
     * 查询会员钱包账户列表
     */
    @SaCheckPermission("member:walletAccount:list")
    @GetMapping("/list")
    public TableDataInfo<MemberWalletAccountVo> list(MemberWalletAccountBo bo, PageQuery pageQuery) {
        return walletService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询会员钱包账户
     */
    @SaCheckPermission("member:walletAccount:query")
    @GetMapping("/{id}")
    public R<MemberWalletAccountVo> getById(@NotNull(message = "钱包账户ID不能为空") @PathVariable Long id) {
        MemberWalletAccountVo account = walletService.queryById(id);
        return R.ok(account);
    }

    /**
     * 修改会员钱包账户
     */
    @SaCheckPermission("member:walletAccount:edit")
    @Log(title = "修改会员钱包账户", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Void> edit(@Validated @RequestBody MemberWalletAccountBo bo) {
        return toAjax(walletService.updateByBo(bo));
    }
    /**
     * 删除会员钱包账户
     *
     * @param ids 主键串
     */
    @SaCheckPermission("member:walletAccount:remove")
    @Log(title = "会员钱包账户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(walletService.deleteWithValidByIds(List.of(ids), true));
    }


    /**
     * 充值
     */
    @Log(title = "钱包充值", businessType = BusinessType.INSERT)
    @PostMapping("/recharge")
    public R<RechargeResultVo> recharge(@Validated @RequestBody RechargeRequestDto request) {
        RechargeResultVo result = walletService.recharge(request);
        return R.ok(result);
    }

    /**
     * 消费
     */
    @Log(title = "钱包消费", businessType = BusinessType.UPDATE)
    @PostMapping("/consume")
    public R<WalletOperationResultVo> consume(@Validated @RequestBody ConsumeRequestDto request) {
        WalletOperationResultVo result = walletService.consume(request);
        if (result.getSuccess()) {
            return R.ok(result);
        }
        return R.fail(result.getErrorMessage());
    }

    /**
     * 创建钱包账户
     */
    @Log(title = "创建钱包账户", businessType = BusinessType.INSERT)
    @PostMapping("/{memberId}/create")
    public R<Void> createWalletAccount(@NotNull(message = "会员ID不能为空") @PathVariable Long memberId) {
        boolean result = walletService.createWalletAccount(memberId);
        return toAjax(result);
    }

}
