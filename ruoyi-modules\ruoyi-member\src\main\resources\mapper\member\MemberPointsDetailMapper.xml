<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.member.mapper.MemberPointsDetailMapper">

    <resultMap type="org.dromara.member.domain.vo.MemberPointsDetailVo" id="MemberPointsDetailResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="businessType"    column="business_type"    />
        <result property="pointsChange"    column="points_change"    />
        <result property="pointsBefore"    column="points_before"    />
        <result property="pointsAfter"    column="points_after"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="consumeAmount"    column="consume_amount"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

</mapper>
