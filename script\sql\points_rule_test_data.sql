-- 积分规则配置测试数据

-- 清理现有测试数据
DELETE FROM points_rule WHERE rule_name LIKE 'TEST_%';

-- ========== points_rule 测试数据 ==========

INSERT INTO points_rule (
    rule_name, min_amount, max_amount, points_ratio, fixed_points, 
    start_time, end_time, status, priority, remark, 
    create_dept, create_by, create_time, update_by, update_time, del_flag, tenant_id
) VALUES 

-- 1. 固定积分规则
-- 满2元送3积分
('TEST_满2元送3积分', 2.00, 4.99, NULL, 3, 
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '0', 1, '消费满2元但不满5元时获得3积分',
 103, 1, NOW(), 1, NOW(), '0', '000000'),

-- 满5元送10积分
('TEST_满5元送10积分', 5.00, 9.99, NULL, 10,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '0', 2, '消费满5元但不满10元时获得10积分',
 103, 1, NOW(), 1, NOW(), '0', '000000'),

-- 满20元送30积分
('TEST_满20元送30积分', 20.00, 49.99, NULL, 30,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '0', 3, '消费满20元但不满50元时获得30积分',
 103, 1, NOW(), 1, NOW(), '0', '000000'),

-- 满100元送150积分
('TEST_满100元送150积分', 100.00, 199.99, NULL, 150,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '0', 4, '消费满100元但不满200元时获得150积分',
 103, 1, NOW(), 1, NOW(), '0', '000000'),

-- 2. 比例积分规则
-- 满10元按1:1比例送积分
('TEST_满10元按1比1送积分', 10.00, NULL, 1.0000, NULL,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '0', 5, '消费满10元时按1元=1积分的比例获得积分',
 103, 1, NOW(), 1, NOW(), '0', '000000'),

-- 满50元按1:2比例送积分
('TEST_满50元按1比2送积分', 50.00, NULL, 2.0000, NULL,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '0', 6, '消费满50元时按1元=2积分的比例获得积分',
 103, 1, NOW(), 1, NOW(), '0', '000000'),

-- 满200元按1:1.5比例送积分
('TEST_满200元按1比1.5送积分', 200.00, NULL, 1.5000, NULL,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '0', 7, '消费满200元时按1元=1.5积分的比例获得积分',
 103, 1, NOW(), 1, NOW(), '0', '000000'),

-- 3. 特殊积分规则
-- 新用户首购双倍积分
('TEST_新用户首购双倍积分', 1.00, 99.99, 2.0000, NULL,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '0', 8, '新用户首次购买双倍积分，消费1-99.99元',
 103, 1, NOW(), 1, NOW(), '0', '000000'),

-- VIP会员专享积分
('TEST_VIP会员专享积分', 1.00, NULL, 1.2000, NULL,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '0', 9, 'VIP会员专享1.2倍积分',
 103, 1, NOW(), 1, NOW(), '0', '000000'),

-- 周末购物积分加成
('TEST_周末购物积分加成', 10.00, NULL, 1.5000, NULL,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '0', 10, '周末购物1.5倍积分加成',
 103, 1, NOW(), 1, NOW(), '0', '000000'),

-- 4. 限时积分规则
-- 双11特惠积分
('TEST_双11特惠积分', 50.00, NULL, 3.0000, NULL,
 '2024-11-11 00:00:00', '2024-11-11 23:59:59', '0', 15, '双11当天消费3倍积分',
 103, 1, NOW(), 1, NOW(), '0', '000000'),

-- 年终大促积分
('TEST_年终大促积分', 100.00, 999.99, NULL, 200,
 '2024-12-20 00:00:00', '2024-12-31 23:59:59', '0', 12, '年终大促期间满100元送200积分',
 103, 1, NOW(), 1, NOW(), '0', '000000'),

-- 5. 小额消费积分规则
-- 满1元送1积分
('TEST_满1元送1积分', 1.00, 1.99, NULL, 1,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '0', 0, '消费满1元但不满2元时获得1积分',
 103, 1, NOW(), 1, NOW(), '0', '000000'),

-- 6. 高额消费积分规则
-- 满500元按0.8比例送积分
('TEST_满500元按0.8比例送积分', 500.00, NULL, 0.8000, NULL,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '0', 11, '消费满500元时按1元=0.8积分的比例获得积分',
 103, 1, NOW(), 1, NOW(), '0', '000000'),

-- 7. 已禁用规则（用于测试）
-- 已停用的积分规则
('TEST_已停用积分规则', 10.00, NULL, 1.0000, NULL,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '1', 5, '已停用的测试积分规则',
 103, 1, NOW(), 1, NOW(), '0', '000000'),

-- 8. 过期规则（用于测试）
-- 已过期的积分规则
('TEST_已过期积分规则', 10.00, NULL, 2.0000, NULL,
 '2024-01-01 00:00:00', '2024-06-30 23:59:59', '0', 5, '已过期的测试积分规则',
 103, 1, NOW(), 1, NOW(), '0', '000000');

-- ========== 查询测试数据 ==========

-- 查看所有测试积分规则
SELECT 
    '=== 积分规则测试数据 ===' as info,
    '' as id, '' as rule_name, '' as min_amount, '' as max_amount, 
    '' as points_ratio, '' as fixed_points, '' as status, '' as priority;

SELECT 
    id, rule_name, min_amount, 
    IFNULL(max_amount, '无上限') as max_amount,
    IFNULL(points_ratio, '无') as points_ratio,
    IFNULL(fixed_points, '无') as fixed_points,
    CASE status WHEN '0' THEN '启用' WHEN '1' THEN '禁用' END as status,
    priority,
    remark
FROM points_rule 
WHERE rule_name LIKE 'TEST_%'
ORDER BY priority DESC, id;

-- 按规则类型分类统计
SELECT 
    '=== 规则类型统计 ===' as info,
    '' as rule_type, '' as count;

SELECT 
    CASE 
        WHEN fixed_points IS NOT NULL THEN '固定积分规则'
        WHEN points_ratio IS NOT NULL THEN '比例积分规则'
        ELSE '未知类型'
    END as rule_type,
    COUNT(*) as count
FROM points_rule 
WHERE rule_name LIKE 'TEST_%'
GROUP BY 
    CASE 
        WHEN fixed_points IS NOT NULL THEN '固定积分规则'
        WHEN points_ratio IS NOT NULL THEN '比例积分规则'
        ELSE '未知类型'
    END;

-- 按状态统计
SELECT 
    '=== 规则状态统计 ===' as info,
    '' as status, '' as count;

SELECT 
    CASE status WHEN '0' THEN '启用' WHEN '1' THEN '禁用' END as status,
    COUNT(*) as count
FROM points_rule 
WHERE rule_name LIKE 'TEST_%'
GROUP BY status;

-- ========== 测试场景说明 ==========
SELECT '=== 测试场景说明 ===' as info;
SELECT '消费金额：3元' as scenario, '预期匹配：满2元送3积分，获得3积分' as expected;
SELECT '消费金额：8元' as scenario, '预期匹配：满5元送10积分，获得10积分' as expected;
SELECT '消费金额：15元' as scenario, '预期匹配：满10元按1:1比例，获得15积分' as expected;
SELECT '消费金额：25元' as scenario, '预期匹配：满20元送30积分，获得30积分' as expected;
SELECT '消费金额：60元' as scenario, '预期匹配：满50元按1:2比例，获得120积分' as expected;
SELECT '消费金额：150元' as scenario, '预期匹配：满100元送150积分，获得150积分' as expected;
SELECT '消费金额：250元' as scenario, '预期匹配：满200元按1:1.5比例，获得375积分' as expected;
SELECT '消费金额：600元' as scenario, '预期匹配：满500元按0.8比例，获得480积分' as expected;

-- ========== 清理脚本（可选执行） ==========
SELECT '=== 清理脚本（需要时执行） ===' as info;
SELECT 'DELETE FROM points_rule WHERE rule_name LIKE "TEST_%";' as cleanup_sql;
