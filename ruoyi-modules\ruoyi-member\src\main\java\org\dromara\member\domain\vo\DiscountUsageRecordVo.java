package org.dromara.member.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.member.domain.DiscountUsageRecord;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 优惠使用记录视图对象 discount_usage_record
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = DiscountUsageRecord.class)
public class DiscountUsageRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 会员ID
     */
    @ExcelProperty(value = "会员ID")
    private Long memberId;

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 使用的规则ID
     */
    @ExcelProperty(value = "使用的规则ID")
    private Long ruleId;

    /**
     * 规则名称（冗余字段）
     */
    @ExcelProperty(value = "规则名称", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "冗=余字段")
    private String ruleName;

    /**
     * 规则类型
     */
    @ExcelProperty(value = "规则类型")
    private String ruleType;

    /**
     * 原始金额
     */
    @ExcelProperty(value = "原始金额")
    private Long originalAmount;

    /**
     * 优惠金额
     */
    @ExcelProperty(value = "优惠金额")
    private Long discountAmount;

    /**
     * 最终金额
     */
    @ExcelProperty(value = "最终金额")
    private Long finalAmount;

    /**
     * 适用商品信息（JSON格式）
     */
    @ExcelProperty(value = "适用商品信息", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "J=SON格式")
    private String applicableProducts;

    /**
     * 使用时间
     */
    @ExcelProperty(value = "使用时间")
    private Date usageTime;

    /**
     * 状态：1-已使用，2-已退款
     */
    @ExcelProperty(value = "状态：1-已使用，2-已退款")
    private String status;

    /**
     * 备注说明
     */
    @ExcelProperty(value = "备注说明")
    private String remark;


}
