package org.dromara.member.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.member.domain.DiscountUsageRecord;
import org.dromara.common.core.annotation.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 优惠使用记录视图对象 discount_usage_record
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = DiscountUsageRecord.class)
public class DiscountUsageRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 会员ID
     */
    @ExcelProperty(value = "会员ID")
    private Long memberId;

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 使用的规则ID
     */
    @ExcelProperty(value = "规则ID")
    private Long ruleId;

    /**
     * 规则名称（冗余字段）
     */
    @ExcelProperty(value = "规则名称")
    private String ruleName;

    /**
     * 规则类型
     */
    @ExcelProperty(value = "规则类型", converter = org.dromara.common.excel.convert.ExcelDictConvert.class)
    @org.dromara.common.excel.annotation.ExcelDictFormat(dictType = "discount_rule_type")
    private String ruleType;

    /**
     * 原始金额
     */
    @ExcelProperty(value = "原始金额")
    private BigDecimal originalAmount;

    /**
     * 优惠金额
     */
    @ExcelProperty(value = "优惠金额")
    private BigDecimal discountAmount;

    /**
     * 最终金额
     */
    @ExcelProperty(value = "最终金额")
    private BigDecimal finalAmount;

    /**
     * 适用商品信息（JSON格式）
     */
    @ExcelProperty(value = "适用商品信息")
    private String applicableProducts;

    /**
     * 使用时间
     */
    @ExcelProperty(value = "使用时间")
    private Date usageTime;

    /**
     * 状态：1-已使用，2-已退款
     */
    @ExcelProperty(value = "状态", converter = org.dromara.common.excel.convert.ExcelDictConvert.class)
    @org.dromara.common.excel.annotation.ExcelDictFormat(dictType = "discount_usage_status")
    private String status;

    /**
     * 备注说明
     */
    @ExcelProperty(value = "备注说明")
    private String remark;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

}
