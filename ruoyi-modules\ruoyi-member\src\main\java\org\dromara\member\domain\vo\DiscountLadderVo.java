package org.dromara.member.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.member.domain.DiscountLadder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 优惠规则配置视图对象 discount_rule
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = DiscountLadder.class)
public class DiscountLadderVo implements Serializable {
    private Long id;

    private Long ruleId;

    private BigDecimal minAmount;

    private BigDecimal maxAmount;

    private BigDecimal discountValue;

    private Integer sortOrder;

    private Date createTime;

    private String tenantId;
}
