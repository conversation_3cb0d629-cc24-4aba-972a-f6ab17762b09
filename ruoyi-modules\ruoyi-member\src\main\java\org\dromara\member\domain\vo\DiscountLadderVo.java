package org.dromara.member.domain.vo;

import org.dromara.member.domain.DiscountLadder;
import org.dromara.common.core.annotation.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 阶梯优惠配置视图对象 discount_ladder
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@AutoMapper(target = DiscountLadder.class)
public class DiscountLadderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 关联规则ID
     */
    private Long ruleId;

    /**
     * 最小金额
     */
    private BigDecimal minAmount;

    /**
     * 最大金额
     */
    private BigDecimal maxAmount;

    /**
     * 优惠值
     */
    private BigDecimal discountValue;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 租户编号
     */
    private String tenantId;

}
