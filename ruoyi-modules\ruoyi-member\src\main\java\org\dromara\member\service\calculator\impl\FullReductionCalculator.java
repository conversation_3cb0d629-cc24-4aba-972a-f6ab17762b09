package org.dromara.member.service.calculator.impl;

import org.dromara.member.domain.dto.DiscountRequest;
import org.dromara.member.domain.vo.DiscountRuleVo;
import org.dromara.member.service.calculator.DiscountCalculator;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 满减优惠计算器
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Component("fullReductionCalculator")
public class FullReductionCalculator implements DiscountCalculator {

    @Override
    public BigDecimal calculateDiscount(DiscountRuleVo rule, DiscountRequest request) {
        if (!isApplicable(rule, request)) {
            return BigDecimal.ZERO;
        }

        // 满减优惠：直接返回优惠值
        return rule.getDiscountValue();
    }

    @Override
    public boolean isApplicable(DiscountRuleVo rule, DiscountRequest request) {
        // 检查金额范围
        BigDecimal amount = request.getOriginalAmount();
        
        // 检查最小金额
        if (amount.compareTo(rule.getMinAmount()) < 0) {
            return false;
        }
        
        // 检查最大金额（如果设置了）
        if (rule.getMaxAmount() != null && amount.compareTo(rule.getMaxAmount()) > 0) {
            return false;
        }
        
        return true;
    }

    @Override
    public String getSupportedRuleType() {
        return "1"; // 满减
    }

}
