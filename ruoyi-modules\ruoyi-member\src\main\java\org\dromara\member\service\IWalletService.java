package org.dromara.member.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.member.domain.bo.MemberWalletAccountBo;
import org.dromara.member.domain.dto.ConsumeRequestDto;
import org.dromara.member.domain.dto.RechargeRequestDto;
import org.dromara.member.domain.dto.RefundRequestDto;
import org.dromara.member.domain.vo.MemberWalletAccountVo;
import org.dromara.member.domain.vo.RechargeResultVo;
import org.dromara.member.domain.vo.WalletBalanceVo;
import org.dromara.member.domain.vo.WalletOperationResultVo;

import java.util.List;

/**
 * 钱包服务接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IWalletService {

    /**
     * 获取钱包余额
     *
     * @param memberId 会员ID
     * @return 钱包余额信息
     */
    WalletBalanceVo getWalletBalance(Long memberId);

    /**
     * 充值
     *
     * @param request 充值请求
     * @return 充值结果
     */
    RechargeResultVo recharge(RechargeRequestDto request);

    /**
     * 消费
     *
     * @param request 消费请求
     * @return 消费结果
     */
    WalletOperationResultVo consume(ConsumeRequestDto request);

    /**
     * 退款
     *
     * @param request 退款请求
     * @return 退款结果
     */
    WalletOperationResultVo refund(RefundRequestDto request);

    /**
     * 冻结余额
     *
     * @param memberId 会员ID
     * @param amount 冻结金额
     * @param reason 冻结原因
     * @return 是否成功
     */
    Boolean freezeBalance(Long memberId, java.math.BigDecimal amount, String reason);

    /**
     * 解冻余额
     *
     * @param memberId 会员ID
     * @param amount 解冻金额
     * @param reason 解冻原因
     * @return 是否成功
     */
    Boolean unfreezeBalance(Long memberId, java.math.BigDecimal amount, String reason);

    /**
     * 创建钱包账户
     *
     * @param memberId 会员ID
     * @return 是否创建成功
     */
    Boolean createWalletAccount(Long memberId);

    /**
     * 检查余额是否充足
     *
     * @param memberId 会员ID
     * @param amount 需要的金额
     * @return 是否充足
     */
    Boolean checkSufficientBalance(Long memberId, java.math.BigDecimal amount);

    /**
     * 查询会员钱包账户列表
     */
    TableDataInfo<MemberWalletAccountVo> queryPageList(MemberWalletAccountBo bo, PageQuery pageQuery);

    /**
     * 查询会员钱包账户
     */
    MemberWalletAccountVo queryById(Long id);

    /**
     * 修改会员钱包账户
     */
    Boolean updateByBo(MemberWalletAccountBo bo);

    /**
     * 校验并批量删除会员钱包账户信息
     */
    Boolean deleteWithValidByIds(List<Long> ids, boolean b);
}
