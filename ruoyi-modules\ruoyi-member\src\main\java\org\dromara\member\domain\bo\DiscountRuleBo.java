package org.dromara.member.domain.bo;

import org.dromara.member.domain.DiscountRule;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;

/**
 * 优惠规则配置业务对象 discount_rule
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = DiscountRule.class, reverseConvertGenerate = false)
public class DiscountRuleBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则类型：1-满减，2-折扣，3-买赠，4-阶梯满减
     */
    private String ruleType;

    /**
     * 最小消费金额
     */
    private Long minAmount;

    /**
     * 最大消费金额（null表示无上限）
     */
    @NotNull(message = "最大消费金额（null表示无上限）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long maxAmount;

    /**
     * 优惠值（满减金额或折扣比例）
     */
    private Long discountValue;

    /**
     * 最大优惠金额（折扣时使用）
     */
    @NotNull(message = "最大优惠金额（折扣时使用）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long maxDiscountAmount;

    /**
     * 适用商品（JSON格式，null表示全商品）
     */
    @NotBlank(message = "适用商品（JSON格式，null表示全商品）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applicableProducts;

    /**
     * 排除商品（JSON格式）
     */
    @NotBlank(message = "排除商品（JSON格式）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String excludeProducts;

    /**
     * 用户限制类型：0-无限制，1-每人限用，2-新用户专享
     */
    @NotBlank(message = "用户限制类型：0-无限制，1-每人限用，2-新用户专享不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userLimitType;

    /**
     * 每人限用次数
     */
    @NotNull(message = "每人限用次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userLimitCount;

    /**
     * 总使用次数限制
     */
    @NotNull(message = "总使用次数限制不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long totalLimitCount;

    /**
     * 已使用次数
     */
    @NotNull(message = "已使用次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long usedCount;

    /**
     * 规则生效开始时间
     */
    @NotNull(message = "规则生效开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date startTime;

    /**
     * 规则生效结束时间
     */
    @NotNull(message = "规则生效结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date endTime;

    /**
     * 状态：0-启用，1-禁用
     */
    private String status;

    /**
     * 优先级（数字越大优先级越高）
     */
    private Long priority;

    /**
     * 是否可叠加：0-不可叠加，1-可叠加
     */
    @NotBlank(message = "是否可叠加：0-不可叠加，1-可叠加不能为空", groups = { AddGroup.class, EditGroup.class })
    private String canStack;

    /**
     * 备注说明
     */
    @NotBlank(message = "备注说明不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
