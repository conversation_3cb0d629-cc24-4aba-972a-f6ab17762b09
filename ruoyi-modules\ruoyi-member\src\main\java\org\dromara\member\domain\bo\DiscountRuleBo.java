package org.dromara.member.domain.bo;

import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.member.domain.DiscountRule;
import org.dromara.common.core.annotation.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 优惠规则配置业务对象 discount_rule
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = DiscountRule.class, reverseConvertGenerate = false)
public class DiscountRuleBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 规则名称
     */
    @NotBlank(message = "规则名称不能为空")
    @Size(max = 100, message = "规则名称长度不能超过{max}个字符")
    private String ruleName;

    /**
     * 规则类型：1-满减，2-折扣，3-买赠，4-阶梯满减
     */
    @NotBlank(message = "规则类型不能为空")
    private String ruleType;

    /**
     * 最小消费金额
     */
    @NotNull(message = "最小消费金额不能为空")
    @DecimalMin(value = "0.00", message = "最小消费金额不能小于0")
    private BigDecimal minAmount;

    /**
     * 最大消费金额（null表示无上限）
     */
    @DecimalMin(value = "0.01", message = "最大消费金额必须大于0")
    private BigDecimal maxAmount;

    /**
     * 优惠值（满减金额或折扣比例）
     */
    @NotNull(message = "优惠值不能为空")
    @DecimalMin(value = "0.00", message = "优惠值不能小于0")
    private BigDecimal discountValue;

    /**
     * 最大优惠金额（折扣时使用）
     */
    @DecimalMin(value = "0.01", message = "最大优惠金额必须大于0")
    private BigDecimal maxDiscountAmount;

    /**
     * 适用商品（JSON格式，null表示全商品）
     */
    private String applicableProducts;

    /**
     * 排除商品（JSON格式）
     */
    private String excludeProducts;

    /**
     * 用户限制类型：0-无限制，1-每人限用，2-新用户专享
     */
    private String userLimitType;

    /**
     * 每人限用次数
     */
    @Min(value = 1, message = "每人限用次数必须大于0")
    private Integer userLimitCount;

    /**
     * 总使用次数限制
     */
    @Min(value = 1, message = "总使用次数限制必须大于0")
    private Integer totalLimitCount;

    /**
     * 已使用次数
     */
    private Integer usedCount;

    /**
     * 规则生效开始时间
     */
    private Date startTime;

    /**
     * 规则生效结束时间
     */
    private Date endTime;

    /**
     * 状态：0-启用，1-禁用
     */
    @NotBlank(message = "状态不能为空")
    private String status;

    /**
     * 优先级（数字越大优先级越高）
     */
    @NotNull(message = "优先级不能为空")
    @Min(value = 0, message = "优先级不能小于0")
    private Integer priority;

    /**
     * 是否可叠加：0-不可叠加，1-可叠加
     */
    private String canStack;

    /**
     * 备注说明
     */
    @Size(max = 500, message = "备注说明长度不能超过{max}个字符")
    private String remark;

}
