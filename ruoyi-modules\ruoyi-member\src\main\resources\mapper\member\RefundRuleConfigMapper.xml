<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.member.mapper.RefundRuleConfigMapper">

    <resultMap type="org.dromara.member.domain.RefundRuleConfig" id="RefundRuleConfigResult">
        <result property="id"    column="id"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="refundType"    column="refund_type"    />
        <result property="minOrderAmount"    column="min_order_amount"    />
        <result property="maxOrderAmount"    column="max_order_amount"    />
        <result property="description"    column="description"    />
        <result property="status"    column="status"    />
        <result property="isDefault"    column="is_default"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!-- 查询默认退款规则 -->
    <select id="selectDefaultRule" resultMap="RefundRuleConfigResult">
        SELECT * FROM refund_rule_config
        WHERE status = '0' AND is_default = '1'
        LIMIT 1
    </select>

    <!-- 根据订单金额匹配退款规则 -->
    <select id="selectRuleByOrderAmount" resultMap="RefundRuleConfigResult">
        SELECT * FROM refund_rule_config
        WHERE status = '0'
        AND (min_order_amount IS NULL OR min_order_amount &lt;= #{orderAmount})
        AND (max_order_amount IS NULL OR max_order_amount &gt;= #{orderAmount})
        ORDER BY is_default ASC
        LIMIT 1
    </select>

</mapper> 