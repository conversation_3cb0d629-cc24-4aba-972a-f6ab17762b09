package org.dromara.member.service;

import org.dromara.member.domain.bo.MemberPointsDetailBo;

import java.math.BigDecimal;

/**
 * 积分异步处理服务接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IPointsAsyncService {

    /**
     * 异步处理消费积分获取
     *
     * @param memberId 会员ID
     * @param amount 消费金额
     * @param orderNo 订单号
     */
    void asyncConsumeEarnPoints(Long memberId, BigDecimal amount, String orderNo);

    /**
     * 异步记录积分明细
     *
     * @param detailBo 积分明细信息
     */
    void asyncRecordPointsDetail(MemberPointsDetailBo detailBo);

    /**
     * 异步刷新积分缓存
     *
     * @param memberId 会员ID
     */
    void asyncRefreshMemberPointsCache(Long memberId);

    /**
     * 异步刷新积分规则缓存
     */
    void asyncRefreshRulesCache();
}
