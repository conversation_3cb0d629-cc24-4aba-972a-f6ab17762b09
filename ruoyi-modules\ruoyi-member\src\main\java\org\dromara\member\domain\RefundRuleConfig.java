package org.dromara.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.member.domain.base.SimpleBaseEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 退款规则配置对象 refund_rule_config
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("refund_rule_config")
public class RefundRuleConfig extends SimpleBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 退款类型：1-只退充值金额，2-按比例退款，3-全额退款（包含赠送）
     */
    private String refundType;

    /**
     * 赠送金额退款比例（0-100）
     */
    private BigDecimal bonusRefundRatio;

    /**
     * 最小订单金额
     */
    private BigDecimal minOrderAmount;

    /**
     * 最大订单金额
     */
    private BigDecimal maxOrderAmount;

    /**
     * 状态：0-启用，1-禁用
     */
    private String status;

    /**
     * 是否默认规则：0-否，1-是
     */
    private String isDefault;

    /**
     * 备注说明
     */
    private String remark;

} 