package org.dromara.member.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.member.service.IPointsCacheService;
import org.dromara.member.service.IPointsAsyncService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 积分缓存刷新定时任务
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RequiredArgsConstructor
@Component
@ConditionalOnProperty(value = "points.cache.refresh.enabled", havingValue = "true", matchIfMissing = true)
public class PointsCacheRefreshTask {

    private final IPointsCacheService pointsCacheService;
    private final IPointsAsyncService pointsAsyncService;

    /**
     * 定时刷新积分规则缓存
     * 每30分钟执行一次
     */
    @Scheduled(fixedRate = 30 * 60 * 1000)
    public void refreshRulesCache() {
        try {
            log.debug("开始定时刷新积分规则缓存");
            pointsAsyncService.asyncRefreshRulesCache();
            log.debug("定时刷新积分规则缓存任务提交成功");
        } catch (Exception e) {
            log.error("定时刷新积分规则缓存失败", e);
        }
    }

    /**
     * 定时清理过期缓存
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 60 * 60 * 1000)
    public void cleanExpiredCache() {
        try {
            log.debug("开始定时清理过期缓存");
            // 这里可以添加清理过期缓存的逻辑
            // 由于Redis会自动清理过期key，这里主要做监控和统计
            log.debug("定时清理过期缓存完成");
        } catch (Exception e) {
            log.error("定时清理过期缓存失败", e);
        }
    }

    /**
     * 缓存预热
     * 系统启动后5分钟执行一次，然后每6小时执行一次
     */
    @Scheduled(initialDelay = 5 * 60 * 1000, fixedRate = 6 * 60 * 60 * 1000)
    public void warmUpCache() {
        try {
            log.info("开始缓存预热");
            pointsAsyncService.asyncRefreshRulesCache();
            log.info("缓存预热任务提交成功");
        } catch (Exception e) {
            log.error("缓存预热失败", e);
        }
    }
}
