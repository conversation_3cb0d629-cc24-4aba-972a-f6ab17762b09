package org.dromara.member.exception;

/**
 * 优惠异常类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public class DiscountException extends RuntimeException {

    private String errorCode;

    public DiscountException(String message) {
        super(message);
    }

    public DiscountException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public DiscountException(String message, Throwable cause) {
        super(message, cause);
    }

    public DiscountException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

}
