package org.dromara.member.domain.bo;

import org.dromara.member.domain.MemberWalletDetail;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 钱包流水记录业务对象 member_wallet_detail
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MemberWalletDetail.class, reverseConvertGenerate = false)
public class MemberWalletDetailBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 关联订单号
     */
    @NotBlank(message = "关联订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderNo;

    /**
     * 业务类型：1-充值，2-消费，3-退款，4-转账，5-活动赠送，6-管理员调整
     */
    private String businessType;

    /**
     * 金额类型：1-充值金额，2-赠送金额
     */
    private String amountType;

    /**
     * 变动金额（正数为增加，负数为减少）
     */
    private Long changeAmount;

    /**
     * 变动前余额
     */
    private Long balanceBefore;

    /**
     * 变动后余额
     */
    private Long balanceAfter;

    /**
     * 变动前充值余额
     */
    private Long rechargeBalanceBefore;

    /**
     * 变动后充值余额
     */
    private Long rechargeBalanceAfter;

    /**
     * 变动前赠送余额
     */
    private Long bonusBalanceBefore;

    /**
     * 变动后赠送余额
     */
    private Long bonusBalanceAfter;

    /**
     * 关联活动ID
     */
    @NotNull(message = "关联活动ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long activityId;

    /**
     * 备注说明
     */
    @NotBlank(message = "备注说明不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
