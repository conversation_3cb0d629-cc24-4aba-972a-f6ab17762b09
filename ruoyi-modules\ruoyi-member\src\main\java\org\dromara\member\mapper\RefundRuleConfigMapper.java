package org.dromara.member.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.member.domain.RefundRuleConfig;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.math.BigDecimal;

/**
 * 退款规则配置Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface RefundRuleConfigMapper extends BaseMapperPlus<RefundRuleConfig, RefundRuleConfig> {

    /**
     * 查询默认退款规则
     */
    RefundRuleConfig selectDefaultRule();

    /**
     * 根据订单金额匹配退款规则
     */
    RefundRuleConfig selectRuleByOrderAmount(@Param("orderAmount") BigDecimal orderAmount);

} 