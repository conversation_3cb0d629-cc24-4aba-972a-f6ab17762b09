package org.dromara.member.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.member.domain.RechargeActivityConfig;
import org.dromara.member.service.IRechargeActivityService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 充值活动配置Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/config/recharge-activity")
public class RechargeActivityController extends BaseController {

    private final IRechargeActivityService rechargeActivityService;

    /**
     * 获取充值活动配置列表
     */
    @GetMapping("/list")
    public R<List<RechargeActivityConfig>> list() {
        try {
            List<RechargeActivityConfig> list = rechargeActivityService.selectRechargeActivityList();
            return R.ok(list);
        } catch (Exception e) {
            log.error("获取充值活动配置列表失败", e);
            return R.fail("获取充值活动配置列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取有效的充值活动配置
     */
    @GetMapping("/active")
    public R<List<RechargeActivityConfig>> getActiveConfigs() {
        try {
            List<RechargeActivityConfig> activeConfigs = rechargeActivityService.selectActiveConfigs();
            return R.ok(activeConfigs);
        } catch (Exception e) {
            log.error("获取有效充值活动配置失败", e);
            return R.fail("获取有效充值活动配置失败：" + e.getMessage());
        }
    }

    /**
     * 根据充值金额匹配活动
     */
    @GetMapping("/match")
    public R<RechargeActivityConfig> matchActivity(@RequestParam BigDecimal amount) {
        try {
            if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
                return R.fail("充值金额必须大于0");
            }

            RechargeActivityConfig matchedActivity = rechargeActivityService.matchActivityByAmount(amount);
            if (matchedActivity == null) {
                return R.ok(matchedActivity);
            }

            return R.ok(matchedActivity);
        } catch (Exception e) {
            log.error("匹配充值活动失败", e);
            return R.fail("匹配充值活动失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID获取充值活动配置
     */
    @GetMapping("/{id}")
    public R<RechargeActivityConfig> getInfo(@NotNull(message = "ID不能为空") @PathVariable Long id) {
        try {
            RechargeActivityConfig config = rechargeActivityService.selectRechargeActivityById(id);
            if (config == null) {
                return R.fail("充值活动配置不存在");
            }
            return R.ok(config);
        } catch (Exception e) {
            log.error("获取充值活动配置失败", e);
            return R.fail("获取充值活动配置失败：" + e.getMessage());
        }
    }

    /**
     * 新增充值活动配置
     */
    @Log(title = "新增充值活动配置", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Void> add(@Validated @RequestBody RechargeActivityConfig config) {
        try {
            int result = rechargeActivityService.insertRechargeActivity(config);
            if (result > 0) {
                return R.ok();
            } else {
                return R.fail("新增充值活动配置失败");
            }
        } catch (IllegalArgumentException e) {
            log.warn("新增充值活动配置参数错误：{}", e.getMessage());
            return R.fail("参数错误：" + e.getMessage());
        } catch (Exception e) {
            log.error("新增充值活动配置失败", e);
            return R.fail("新增充值活动配置失败：" + e.getMessage());
        }
    }

    /**
     * 修改充值活动配置
     */
    @Log(title = "修改充值活动配置", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Void> edit(@Validated @RequestBody RechargeActivityConfig config) {
        try {
            int result = rechargeActivityService.updateRechargeActivity(config);
            if (result > 0) {
                return R.ok();
            } else {
                return R.fail("修改充值活动配置失败，可能记录不存在或已被删除");
            }
        } catch (IllegalArgumentException e) {
            log.warn("修改充值活动配置参数错误：{}", e.getMessage());
            return R.fail("参数错误：" + e.getMessage());
        } catch (Exception e) {
            log.error("修改充值活动配置失败", e);
            return R.fail("修改充值活动配置失败：" + e.getMessage());
        }
    }

    /**
     * 删除充值活动配置
     */
    @Log(title = "删除充值活动配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotNull(message = "ID不能为空") @PathVariable Long[] ids) {
        try {
            int result = rechargeActivityService.deleteRechargeActivityByIds(ids);
            if (result > 0) {
                return R.ok();
            } else {
                return R.fail("删除充值活动配置失败，可能记录不存在或已被删除");
            }
        } catch (IllegalArgumentException e) {
            log.warn("删除充值活动配置参数错误：{}", e.getMessage());
            return R.fail("参数错误：" + e.getMessage());
        } catch (Exception e) {
            log.error("删除充值活动配置失败", e);
            return R.fail("删除充值活动配置失败：" + e.getMessage());
        }
    }

    /**
     * 启用充值活动配置
     */
    @Log(title = "启用充值活动配置", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/enable")
    public R<Void> enable(@NotNull(message = "ID不能为空") @PathVariable Long id) {
        try {
            int result = rechargeActivityService.enableRechargeActivity(id);
            if (result > 0) {
                return R.ok();
            } else {
                return R.fail("启用充值活动配置失败，可能记录不存在或已被删除");
            }
        } catch (IllegalArgumentException e) {
            log.warn("启用充值活动配置参数错误：{}", e.getMessage());
            return R.fail("参数错误：" + e.getMessage());
        } catch (Exception e) {
            log.error("启用充值活动配置失败", e);
            return R.fail("启用充值活动配置失败：" + e.getMessage());
        }
    }

    /**
     * 禁用充值活动配置
     */
    @Log(title = "禁用充值活动配置", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/disable")
    public R<Void> disable(@NotNull(message = "ID不能为空") @PathVariable Long id) {
        try {
            int result = rechargeActivityService.disableRechargeActivity(id);
            if (result > 0) {
                return R.ok();
            } else {
                return R.fail("禁用充值活动配置失败，可能记录不存在或已被删除");
            }
        } catch (IllegalArgumentException e) {
            log.warn("禁用充值活动配置参数错误：{}", e.getMessage());
            return R.fail("参数错误：" + e.getMessage());
        } catch (Exception e) {
            log.error("禁用充值活动配置失败", e);
            return R.fail("禁用充值活动配置失败：" + e.getMessage());
        }
    }

}
