# 会员积分模块

## 功能概述

会员积分模块提供完整的积分管理功能，支持消费获得积分、积分规则配置、积分明细查询等功能。

## 主要功能

### 1. 积分规则管理
- 支持灵活的积分规则配置
- 支持固定积分和比例积分两种模式
- 支持时间范围和优先级设置
- 支持规则的启用/禁用

### 2. 积分账户管理
- 自动创建会员积分账户
- 支持积分余额查询
- 使用乐观锁防止并发问题
- 记录总积分和可用积分

### 3. 积分明细记录
- 记录所有积分变动历史
- 支持多种业务类型
- 支持分页查询和导出
- 关联订单号和规则信息

### 4. 消费积分获取
- 根据消费金额自动计算积分
- 支持多种积分规则匹配
- 事务保证数据一致性
- 详细的日志记录

## 数据库表结构

### 1. points_rule - 积分规则配置表
- 支持金额范围配置
- 支持固定积分和比例积分
- 支持时间范围和优先级

### 2. member_points_account - 会员积分账户表
- 记录会员积分余额
- 使用乐观锁版本控制
- 支持多租户

### 3. member_points_detail - 积分明细记录表
- 记录所有积分变动
- 关联业务信息
- 支持历史查询

## API接口

### 积分规则管理
- `GET /member/pointsRule/list` - 查询积分规则列表
- `POST /member/pointsRule` - 新增积分规则
- `PUT /member/pointsRule` - 修改积分规则
- `DELETE /member/pointsRule/{ids}` - 删除积分规则
- `GET /member/pointsRule/active` - 获取有效规则

### 会员积分管理
- `GET /member/points/account/{memberId}` - 获取积分账户信息
- `GET /member/points/history/{memberId}` - 获取积分历史
- `POST /member/points/consume` - 消费获得积分
- `GET /member/points/calculate` - 计算积分
- `GET /member/points/balance/{memberId}` - 获取积分余额
- `POST /member/points/adjust` - 管理员调整积分

## 使用示例

### 1. 配置积分规则
```json
{
  "ruleName": "满10元送10积分",
  "minAmount": 10.00,
  "maxAmount": null,
  "fixedPoints": 10,
  "status": "0",
  "priority": 1,
  "remark": "消费满10元获得10积分"
}
```

### 2. 消费获得积分
```http
POST /member/points/consume
{
  "memberId": 1,
  "amount": 15.50,
  "orderNo": "ORDER_20240101_001"
}
```

### 3. 查询积分余额
```http
GET /member/points/balance/1
```

## 业务规则

### 积分计算规则
1. 按优先级从高到低匹配规则
2. 优先使用固定积分，其次使用比例积分
3. 金额必须在规则的范围内
4. 规则必须在有效时间范围内

### 积分获取流程
1. 接收消费订单信息
2. 根据消费金额匹配积分规则
3. 计算应获得的积分数
4. 更新会员积分账户
5. 记录积分变动明细
6. 返回获得的积分数

## 性能优化

### 1. 数据库优化 ✅
- ✅ 为常用查询字段建立索引（SQL脚本中已定义）
- ✅ 使用乐观锁避免锁竞争（@Version注解 + 乐观锁更新）
- ✅ 分页查询避免大数据量（PageQuery分页）

### 2. 缓存策略 ✅
- ✅ 积分规则缓存到Redis（PointsCacheService实现）
- ✅ 用户积分余额缓存（Redis缓存账户和余额信息）
- ✅ 定时刷新缓存数据（PointsCacheRefreshTask定时任务）

### 3. 并发控制 ✅
- ✅ 乐观锁防止并发更新（version字段乐观锁）
- ✅ 事务保证数据一致性（@Transactional注解）
- ✅ 异步处理提高性能（PointsAsyncService异步服务）

### 4. 性能监控 ✅
- ✅ 方法执行时间监控（PointsPerformanceMonitor切面）
- ✅ 缓存操作监控（AOP监控缓存性能）
- ✅ 异常监控和日志记录

## 扩展功能

### 1. 积分过期
- 支持积分有效期设置
- 定时任务处理过期积分
- 过期通知功能

### 2. 积分抵扣
- 支持积分抵扣消费
- 积分与现金混合支付
- 抵扣规则配置

### 3. 积分等级
- 根据积分设置会员等级
- 不同等级享受不同权益
- 等级升级通知

## 注意事项

1. 积分数据涉及资金安全，必须保证数据准确性
2. 使用事务确保积分变动的原子性
3. 定期备份积分数据
4. 监控异常积分变动
5. 做好日志记录便于问题排查
