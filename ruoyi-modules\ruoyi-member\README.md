# 会员积分模块

## 功能概述

会员积分模块提供完整的积分管理功能，支持消费获得积分、积分规则配置、积分明细查询等功能。

## 主要功能

### 1. 积分规则管理
- 支持灵活的积分规则配置
- 支持固定积分和比例积分两种模式
- 支持时间范围和优先级设置
- 支持规则的启用/禁用

### 2. 积分账户管理
- 自动创建会员积分账户
- 支持积分余额查询
- 使用乐观锁防止并发问题
- 记录总积分和可用积分

### 3. 积分明细记录
- 记录所有积分变动历史
- 支持多种业务类型
- 支持分页查询和导出
- 关联订单号和规则信息

### 4. 消费积分获取
- 根据消费金额自动计算积分
- 支持多种积分规则匹配
- 事务保证数据一致性
- 详细的日志记录

## 数据库表结构

### 1. points_rule - 积分规则配置表
- 支持金额范围配置
- 支持固定积分和比例积分
- 支持时间范围和优先级

### 2. member_points_account - 会员积分账户表
- 记录会员积分余额
- 使用乐观锁版本控制
- 支持多租户

### 3. member_points_detail - 积分明细记录表
- 记录所有积分变动
- 关联业务信息
- 支持历史查询

## API接口

### 积分规则管理
- `GET /member/pointsRule/list` - 查询积分规则列表
- `POST /member/pointsRule` - 新增积分规则
- `PUT /member/pointsRule` - 修改积分规则
- `DELETE /member/pointsRule/{ids}` - 删除积分规则
- `GET /member/pointsRule/active` - 获取有效规则

### 会员积分管理
- `GET /member/points/account/{memberId}` - 获取积分账户信息
- `GET /member/points/history/{memberId}` - 获取积分历史
- `POST /member/points/consume` - 消费获得积分
- `GET /member/points/calculate` - 计算积分
- `GET /member/points/balance/{memberId}` - 获取积分余额
- `POST /member/points/adjust` - 管理员调整积分

## 使用示例

### 1. 配置积分规则
```json
{
  "ruleName": "满10元送10积分",
  "minAmount": 10.00,
  "maxAmount": null,
  "fixedPoints": 10,
  "status": "0",
  "priority": 1,
  "remark": "消费满10元获得10积分"
}
```

### 2. 消费获得积分
```http
POST /member/points/consume
{
  "memberId": 1,
  "amount": 15.50,
  "orderNo": "ORDER_20240101_001"
}
```

### 3. 查询积分余额
```http
GET /member/points/balance/1
```

## 业务规则

### 积分计算规则
1. 按优先级从高到低匹配规则
2. 优先使用固定积分，其次使用比例积分
3. 金额必须在规则的范围内
4. 规则必须在有效时间范围内

### 积分获取流程
1. 接收消费订单信息
2. 根据消费金额匹配积分规则
3. 计算应获得的积分数
4. 更新会员积分账户
5. 记录积分变动明细
6. 返回获得的积分数

## 性能优化

### 1. 数据库优化 ✅
- ✅ 为常用查询字段建立索引（SQL脚本中已定义）
- ✅ 使用乐观锁避免锁竞争（@Version注解 + 乐观锁更新）
- ✅ 分页查询避免大数据量（PageQuery分页）

### 2. 缓存策略 ✅
- ✅ 积分规则缓存到Redis（PointsCacheService实现）
- ✅ 用户积分余额缓存（Redis缓存账户和余额信息）
- ✅ 定时刷新缓存数据（PointsCacheRefreshTask定时任务）

### 3. 并发控制 ✅
- ✅ 乐观锁防止并发更新（version字段乐观锁）
- ✅ 事务保证数据一致性（@Transactional注解）
- ✅ 异步处理提高性能（PointsAsyncService异步服务）

### 4. 性能监控 ✅
- ✅ 方法执行时间监控（PointsPerformanceMonitor切面）
- ✅ 缓存操作监控（AOP监控缓存性能）
- ✅ 异常监控和日志记录

## 扩展功能

### 1. 积分过期
- 支持积分有效期设置
- 定时任务处理过期积分
- 过期通知功能

### 2. 积分抵扣
- 支持积分抵扣消费
- 积分与现金混合支付
- 抵扣规则配置

### 3. 积分等级
- 根据积分设置会员等级
- 不同等级享受不同权益
- 等级升级通知

## 优惠体系

### 功能概述
优惠体系提供完整的优惠管理功能，支持满减、折扣、阶梯优惠等多种优惠类型，与积分体系和储值体系形成完整的会员权益体系。

### 主要功能

#### 1. 优惠规则管理
- 支持多种优惠类型（满减、折扣、买赠、阶梯满减）
- 支持商品适用范围配置
- 支持用户使用限制和总量限制
- 支持规则优先级和叠加策略

#### 2. 优惠计算引擎
- 智能匹配最优优惠方案
- 支持多规则叠加计算
- 高性能缓存优化
- 实时优惠金额计算

#### 3. 优惠使用记录
- 记录所有优惠使用明细
- 支持优惠退款处理
- 防重复使用控制
- 使用统计分析

### 数据库表结构

#### 1. discount_rule - 优惠规则配置表
- 支持多种优惠类型配置
- 支持商品范围和用户限制
- 支持时间范围和优先级

#### 2. discount_ladder - 阶梯优惠配置表
- 支持阶梯式优惠配置
- 关联主规则表
- 支持复杂优惠策略

#### 3. discount_usage_record - 优惠使用记录表
- 记录所有优惠使用历史
- 关联订单和规则信息
- 支持退款状态管理

#### 4. member_discount_stats - 会员优惠使用统计表
- 记录会员使用次数统计
- 使用乐观锁防并发
- 支持使用限制验证

### API接口

#### 优惠规则管理
- `GET /member/discountRule/list` - 查询优惠规则列表
- `POST /member/discountRule` - 新增优惠规则
- `PUT /member/discountRule` - 修改优惠规则
- `DELETE /member/discountRule/{ids}` - 删除优惠规则
- `GET /member/discountRule/active` - 获取有效规则
- `GET /member/discountRule/applicable` - 获取适用规则

#### 优惠计算和使用
- `POST /member/discount/calculate` - 计算订单优惠
- `POST /member/discount/preview` - 预览优惠效果
- `POST /member/discount/plans` - 获取优惠方案
- `POST /member/discount/use` - 使用优惠
- `POST /member/discount/refund` - 优惠退款

#### 优惠记录查询
- `GET /member/discount/usage/history/{memberId}` - 查询会员优惠使用历史

### 使用示例

#### 1. 配置满减优惠规则
```json
{
  "ruleName": "满100减20",
  "ruleType": "1",
  "minAmount": 100.00,
  "discountValue": 20.00,
  "status": "0",
  "priority": 1,
  "remark": "消费满100元减20元"
}
```

#### 2. 配置折扣优惠规则
```json
{
  "ruleName": "全场8折",
  "ruleType": "2",
  "minAmount": 0.01,
  "discountValue": 0.80,
  "maxDiscountAmount": 100.00,
  "status": "0",
  "priority": 2,
  "remark": "全场商品8折，最高优惠100元"
}
```

#### 3. 配置阶梯满减规则
```json
{
  "ruleName": "阶梯满减",
  "ruleType": "4",
  "minAmount": 100.00,
  "status": "0",
  "priority": 3,
  "remark": "阶梯式满减优惠"
}
```

#### 4. 计算订单优惠
```http
POST /member/discount/calculate
{
  "memberId": 1,
  "originalAmount": 150.00,
  "isNewUser": false,
  "products": [
    {
      "productId": 1,
      "productName": "商品A",
      "price": 50.00,
      "quantity": 3,
      "totalAmount": 150.00
    }
  ]
}
```

#### 5. 使用优惠
```http
POST /member/discount/use
{
  "memberId": 1,
  "orderNo": "ORDER_20240101_001",
  "originalAmount": 150.00,
  "ruleIds": [1],
  "remark": "使用满减优惠"
}
```

### 业务规则

#### 优惠计算规则
1. 按优先级从高到低匹配规则
2. 检查金额范围、时间范围、使用限制
3. 支持单规则和多规则叠加
4. 自动选择最优优惠方案

#### 优惠使用流程
1. 接收优惠使用请求
2. 验证规则有效性和可用性
3. 使用分布式锁防止并发问题
4. 计算优惠金额并记录使用明细
5. 更新规则和会员使用统计
6. 返回使用结果

#### 退款处理流程
1. 查询原优惠使用记录
2. 更新使用记录状态为已退款
3. 减少规则和会员使用统计
4. 支持部分退款和全额退款

### 性能优化

#### 1. 缓存策略
- 优惠规则缓存到Redis
- 会员使用统计缓存
- 规则详情缓存

#### 2. 并发控制
- 使用分布式锁防止重复使用
- 乐观锁更新使用统计
- 事务保证数据一致性

#### 3. 计算优化
- 策略模式实现不同类型计算器
- 规则按优先级排序
- 支持规则叠加计算

### 扩展功能

#### 1. 优惠券系统
- 支持优惠券发放和使用
- 优惠券与优惠规则结合
- 优惠券过期处理

#### 2. 营销活动
- 支持限时活动优惠
- 新用户专享优惠
- 会员等级优惠

#### 3. 商品范围控制
- 支持指定商品优惠
- 支持商品分类优惠
- 支持排除商品设置

## 注意事项

1. 积分数据涉及资金安全，必须保证数据准确性
2. 使用事务确保积分变动的原子性
3. 定期备份积分数据
4. 监控异常积分变动
5. 做好日志记录便于问题排查
