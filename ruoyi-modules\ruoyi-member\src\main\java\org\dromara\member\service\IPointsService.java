package org.dromara.member.service;

import org.dromara.member.domain.vo.MemberPointsAccountVo;
import org.dromara.member.domain.vo.MemberPointsDetailVo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.math.BigDecimal;

/**
 * 积分服务门面接口
 * 提供对外的积分相关服务
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IPointsService {

    /**
     * 消费获得积分
     *
     * @param memberId 会员ID
     * @param amount 消费金额
     * @param orderNo 订单号（可选）
     * @return 获得的积分数量，0表示未获得积分
     */
    Long consumeEarnPoints(Long memberId, BigDecimal amount, String orderNo);

    /**
     * 根据消费金额计算可获得的积分
     *
     * @param amount 消费金额
     * @return 可获得的积分数量
     */
    Long calculatePointsByAmount(BigDecimal amount);

    /**
     * 获取会员积分余额
     *
     * @param memberId 会员ID
     * @return 积分余额
     */
    Long getMemberPointsBalance(Long memberId);

    /**
     * 获取会员积分账户信息
     *
     * @param memberId 会员ID
     * @return 积分账户信息
     */
    MemberPointsAccountVo getMemberPointsAccount(Long memberId);

    /**
     * 获取会员积分历史记录
     *
     * @param memberId 会员ID
     * @param pageQuery 分页参数
     * @return 积分历史记录
     */
    TableDataInfo<MemberPointsDetailVo> getMemberPointsHistory(Long memberId, PageQuery pageQuery);

    /**
     * 创建会员积分账户
     *
     * @param memberId 会员ID
     * @return 是否创建成功
     */
    Boolean createMemberPointsAccount(Long memberId);

    /**
     * 管理员调整积分
     *
     * @param memberId 会员ID
     * @param pointsChange 积分变动数量（正数为增加，负数为减少）
     * @param remark 备注
     * @return 是否调整成功
     */
    Boolean adjustMemberPoints(Long memberId, Long pointsChange, String remark);
}
