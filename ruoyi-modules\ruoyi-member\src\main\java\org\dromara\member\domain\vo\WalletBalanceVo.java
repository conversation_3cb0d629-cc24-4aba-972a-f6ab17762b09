package org.dromara.member.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 钱包余额VO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class WalletBalanceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 会员昵称
     */
    private String nickname;

    /**
     * 总余额
     */
    private BigDecimal totalBalance;

    /**
     * 充值余额
     */
    private BigDecimal rechargeBalance;

    /**
     * 赠送余额
     */
    private BigDecimal bonusBalance;

    /**
     * 冻结余额
     */
    private BigDecimal frozenBalance;

    /**
     * 可用余额（总余额-冻结余额）
     */
    private BigDecimal availableBalance;

} 