package org.dromara.member.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.member.domain.ConsumeStrategyConfig;
import org.dromara.member.service.IConsumeStrategyConfigService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 消费策略配置Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/config/consume-strategy")
public class ConsumeStrategyController extends BaseController {

    private final IConsumeStrategyConfigService consumeStrategyConfigService;

    /**
     * 分页查询消费策略配置
     */
    @GetMapping("/page")
    @Log(title = "消费策略配置")
    public R<TableDataInfo<ConsumeStrategyConfig>> page(PageQuery pageQuery, ConsumeStrategyConfig config) {
        try {
            Page<ConsumeStrategyConfig> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
            var result = consumeStrategyConfigService.selectPage(page, config);
            
            log.info("分页查询消费策略配置成功，查询条件：{}", config.getStrategyName());
            return R.ok(TableDataInfo.build(result));
            
        } catch (Exception e) {
            log.error("分页查询消费策略配置失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取消费策略配置列表
     */
    @GetMapping("/list")
    public R<List<ConsumeStrategyConfig>> list(ConsumeStrategyConfig config) {
        try {
            List<ConsumeStrategyConfig> list = consumeStrategyConfigService.selectList(config);
            
            log.info("查询消费策略配置列表成功，数量：{}", list.size());
            return R.ok(list);
            
        } catch (Exception e) {
            log.error("查询消费策略配置列表失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取默认消费策略配置
     */
    @GetMapping("/default")
    public R<ConsumeStrategyConfig> getDefaultStrategy() {
        try {
            ConsumeStrategyConfig defaultStrategy = consumeStrategyConfigService.selectDefaultStrategy();
            
            if (ObjectUtil.isNull(defaultStrategy)) {
                return R.fail("未找到默认消费策略配置");
            }
            
            log.info("获取默认消费策略配置成功：{}", defaultStrategy.getStrategyName());
            return R.ok(defaultStrategy);
            
        } catch (Exception e) {
            log.error("获取默认消费策略配置失败", e);
            return R.fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID获取消费策略配置
     */
    @GetMapping("/{id}")
    public R<ConsumeStrategyConfig> getInfo(@NotNull(message = "ID不能为空") @PathVariable Long id) {
        try {
            ConsumeStrategyConfig config = consumeStrategyConfigService.selectById(id);
            
            if (ObjectUtil.isNull(config)) {
                return R.fail("消费策略配置不存在");
            }
            
            log.info("获取消费策略配置成功，ID：{}，策略名称：{}", id, config.getStrategyName());
            return R.ok(config);
            
        } catch (Exception e) {
            log.error("获取消费策略配置失败，ID：{}", id, e);
            return R.fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 新增消费策略配置
     */
    @Log(title = "新增消费策略配置")
    @PostMapping()
    public R<Void> add(@Validated @RequestBody ConsumeStrategyConfig config) {
        try {
            Boolean result = consumeStrategyConfigService.insert(config);
            
            if (result) {
                log.info("新增消费策略配置成功：{}", config.getStrategyName());
                return R.ok();
            } else {
                return R.fail("新增消费策略配置失败");
            }
            
        } catch (Exception e) {
            log.error("新增消费策略配置失败", e);
            return R.fail("新增失败：" + e.getMessage());
        }
    }

    /**
     * 修改消费策略配置
     */
    @Log(title = "修改消费策略配置")
    @PutMapping()
    public R<Void> edit(@Validated @RequestBody ConsumeStrategyConfig config) {
        try {
            if (ObjectUtil.isNull(config.getId())) {
                return R.fail("配置ID不能为空");
            }
            
            Boolean result = consumeStrategyConfigService.update(config);
            
            if (result) {
                log.info("修改消费策略配置成功，ID：{}，策略名称：{}", config.getId(), config.getStrategyName());
                return R.ok();
            } else {
                return R.fail("修改消费策略配置失败");
            }
            
        } catch (Exception e) {
            log.error("修改消费策略配置失败", e);
            return R.fail("修改失败：" + e.getMessage());
        }
    }

    /**
     * 删除消费策略配置
     */
    @Log(title = "删除消费策略配置")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotNull(message = "ID不能为空") @PathVariable Long[] ids) {
        try {
            List<Long> idList = Arrays.asList(ids);
            Boolean result = consumeStrategyConfigService.deleteBatch(idList);
            
            if (result) {
                log.info("删除消费策略配置成功，ID：{}", Arrays.toString(ids));
                return R.ok();
            } else {
                return R.fail("删除消费策略配置失败");
            }
            
        } catch (Exception e) {
            log.error("删除消费策略配置失败，ID：{}", Arrays.toString(ids), e);
            return R.fail("删除失败：" + e.getMessage());
        }
    }

    /**
     * 设置默认消费策略
     */
    @Log(title = "设置默认消费策略")
    @PostMapping("/{id}/set-default")
    public R<Void> setDefault(@NotNull(message = "ID不能为空") @PathVariable Long id) {
        try {
            Boolean result = consumeStrategyConfigService.setDefaultStrategy(id);
            
            if (result) {
                log.info("设置默认消费策略成功，ID：{}", id);
                return R.ok();
            } else {
                return R.fail("设置默认消费策略失败");
            }
            
        } catch (Exception e) {
            log.error("设置默认消费策略失败，ID：{}", id, e);
            return R.fail("设置失败：" + e.getMessage());
        }
    }

    /**
     * 启用消费策略配置
     */
    @Log(title = "启用消费策略配置")
    @PostMapping("/{id}/enable")
    public R<Void> enable(@NotNull(message = "ID不能为空") @PathVariable Long id) {
        try {
            Boolean result = consumeStrategyConfigService.changeStatus(id, "0");
            
            if (result) {
                log.info("启用消费策略配置成功，ID：{}", id);
                return R.ok();
            } else {
                return R.fail("启用消费策略配置失败");
            }
            
        } catch (Exception e) {
            log.error("启用消费策略配置失败，ID：{}", id, e);
            return R.fail("启用失败：" + e.getMessage());
        }
    }

    /**
     * 禁用消费策略配置
     */
    @Log(title = "禁用消费策略配置")
    @PostMapping("/{id}/disable")
    public R<Void> disable(@NotNull(message = "ID不能为空") @PathVariable Long id) {
        try {
            Boolean result = consumeStrategyConfigService.changeStatus(id, "1");
            
            if (result) {
                log.info("禁用消费策略配置成功，ID：{}", id);
                return R.ok();
            } else {
                return R.fail("禁用消费策略配置失败");
            }
            
        } catch (Exception e) {
            log.error("禁用消费策略配置失败，ID：{}", id, e);
            return R.fail("禁用失败：" + e.getMessage());
        }
    }

}
