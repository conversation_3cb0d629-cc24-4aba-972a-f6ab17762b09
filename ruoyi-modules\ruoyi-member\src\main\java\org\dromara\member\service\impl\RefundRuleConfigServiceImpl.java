package org.dromara.member.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.member.domain.RefundRuleConfig;
import org.dromara.member.mapper.RefundRuleConfigMapper;
import org.dromara.member.service.IRefundRuleConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 退款规则配置服务实现类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class RefundRuleConfigServiceImpl implements IRefundRuleConfigService {

    private final RefundRuleConfigMapper refundRuleConfigMapper;

    @Override
    public IPage<RefundRuleConfig> selectPage(Page<RefundRuleConfig> page, RefundRuleConfig config) {
        LambdaQueryWrapper<RefundRuleConfig> wrapper = buildQueryWrapper(config);
        wrapper.orderByAsc(RefundRuleConfig::getIsDefault);
        wrapper.orderByDesc(RefundRuleConfig::getCreateTime);
        return refundRuleConfigMapper.selectPage(page, wrapper);
    }

    @Override
    public List<RefundRuleConfig> selectList(RefundRuleConfig config) {
        LambdaQueryWrapper<RefundRuleConfig> wrapper = buildQueryWrapper(config);
        wrapper.orderByAsc(RefundRuleConfig::getIsDefault);
        wrapper.orderByDesc(RefundRuleConfig::getCreateTime);
        return refundRuleConfigMapper.selectList(wrapper);
    }

    @Override
    public RefundRuleConfig selectById(Long id) {
        if (ObjectUtil.isNull(id)) {
            throw new ServiceException("配置ID不能为空");
        }
        return refundRuleConfigMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insert(RefundRuleConfig config) {
        // 验证配置数据
        validateConfig(config);
        
        // 设置默认值
        if (ObjectUtil.isEmpty(config.getStatus())) {
            config.setStatus("0"); // 默认启用
        }
        if (ObjectUtil.isEmpty(config.getIsDefault())) {
            config.setIsDefault("0"); // 默认非默认规则
        }
        
        // 如果设置为默认规则，需要取消其他默认规则
        if ("1".equals(config.getIsDefault())) {
            clearOtherDefaultRules();
        }
        
        int result = refundRuleConfigMapper.insert(config);
        if (result > 0) {
            log.info("新增退款规则配置成功：{}", config.getRuleName());
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(RefundRuleConfig config) {
        if (ObjectUtil.isNull(config.getId())) {
            throw new ServiceException("配置ID不能为空");
        }
        
        // 验证配置是否存在
        RefundRuleConfig existConfig = selectById(config.getId());
        if (ObjectUtil.isNull(existConfig)) {
            throw new ServiceException("配置不存在");
        }
        
        // 验证配置数据
        validateConfig(config);
        
        // 如果设置为默认规则，需要取消其他默认规则
        if ("1".equals(config.getIsDefault())) {
            clearOtherDefaultRules(config.getId());
        }
        
        int result = refundRuleConfigMapper.updateById(config);
        if (result > 0) {
            log.info("更新退款规则配置成功：{}", config.getRuleName());
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        if (ObjectUtil.isNull(id)) {
            throw new ServiceException("配置ID不能为空");
        }
        
        RefundRuleConfig config = selectById(id);
        if (ObjectUtil.isNull(config)) {
            throw new ServiceException("配置不存在");
        }
        
        // 不能删除默认规则
        if ("1".equals(config.getIsDefault())) {
            throw new ServiceException("不能删除默认规则");
        }
        
        int result = refundRuleConfigMapper.deleteById(id);
        if (result > 0) {
            log.info("删除退款规则配置成功：{}", config.getRuleName());
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteBatch(List<Long> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            throw new ServiceException("配置ID列表不能为空");
        }
        
        // 检查是否包含默认规则
        for (Long id : ids) {
            RefundRuleConfig config = selectById(id);
            if (ObjectUtil.isNotNull(config) && "1".equals(config.getIsDefault())) {
                throw new ServiceException("不能删除默认规则：" + config.getRuleName());
            }
        }
        
        int result = refundRuleConfigMapper.deleteBatchIds(ids);
        if (result > 0) {
            log.info("批量删除退款规则配置成功，数量：{}", result);
            return true;
        }
        return false;
    }

    @Override
    public RefundRuleConfig selectDefaultRule() {
        return refundRuleConfigMapper.selectDefaultRule();
    }

    @Override
    public RefundRuleConfig selectRuleByOrderAmount(BigDecimal orderAmount) {
        if (ObjectUtil.isNull(orderAmount) || orderAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        return refundRuleConfigMapper.selectRuleByOrderAmount(orderAmount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeStatus(Long id, String status) {
        if (ObjectUtil.isNull(id)) {
            throw new ServiceException("配置ID不能为空");
        }
        
        if (StringUtils.isBlank(status)) {
            throw new ServiceException("状态不能为空");
        }
        
        RefundRuleConfig existConfig = selectById(id);
        if (ObjectUtil.isNull(existConfig)) {
            throw new ServiceException("配置不存在");
        }
        
        // 如果是默认规则，不能禁用
        if ("1".equals(existConfig.getIsDefault()) && "1".equals(status)) {
            throw new ServiceException("不能禁用默认规则");
        }
        
        RefundRuleConfig config = new RefundRuleConfig();
        config.setId(id);
        config.setStatus(status);
        
        int result = refundRuleConfigMapper.updateById(config);
        if (result > 0) {
            log.info("更新退款规则配置状态成功，ID：{}，状态：{}", id, status);
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setDefaultRule(Long id) {
        if (ObjectUtil.isNull(id)) {
            throw new ServiceException("配置ID不能为空");
        }
        
        RefundRuleConfig existConfig = selectById(id);
        if (ObjectUtil.isNull(existConfig)) {
            throw new ServiceException("配置不存在");
        }
        
        // 验证规则状态是否启用
        if (!"0".equals(existConfig.getStatus())) {
            throw new ServiceException("只能设置启用状态的规则为默认规则");
        }
        
        // 取消其他默认规则
        clearOtherDefaultRules(id);
        
        // 设置为默认规则
        RefundRuleConfig config = new RefundRuleConfig();
        config.setId(id);
        config.setIsDefault("1");
        
        int result = refundRuleConfigMapper.updateById(config);
        if (result > 0) {
            log.info("设置默认退款规则成功，ID：{}", id);
            return true;
        }
        return false;
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<RefundRuleConfig> buildQueryWrapper(RefundRuleConfig config) {
        LambdaQueryWrapper<RefundRuleConfig> wrapper = new LambdaQueryWrapper<>();
        
        if (ObjectUtil.isNotNull(config)) {
            wrapper.like(StringUtils.isNotBlank(config.getRuleName()), 
                        RefundRuleConfig::getRuleName, config.getRuleName());
            wrapper.eq(StringUtils.isNotBlank(config.getRefundType()), 
                      RefundRuleConfig::getRefundType, config.getRefundType());
            wrapper.eq(StringUtils.isNotBlank(config.getStatus()), 
                      RefundRuleConfig::getStatus, config.getStatus());
            wrapper.eq(StringUtils.isNotBlank(config.getIsDefault()), 
                      RefundRuleConfig::getIsDefault, config.getIsDefault());
            wrapper.ge(ObjectUtil.isNotNull(config.getMinOrderAmount()), 
                      RefundRuleConfig::getMinOrderAmount, config.getMinOrderAmount());
            wrapper.le(ObjectUtil.isNotNull(config.getMaxOrderAmount()), 
                      RefundRuleConfig::getMaxOrderAmount, config.getMaxOrderAmount());
        }
        
        return wrapper;
    }

    /**
     * 验证配置数据
     */
    private void validateConfig(RefundRuleConfig config) {
        if (StringUtils.isBlank(config.getRuleName())) {
            throw new ServiceException("规则名称不能为空");
        }
        
        if (StringUtils.isBlank(config.getRefundType())) {
            throw new ServiceException("退款类型不能为空");
        }
        
        // 验证退款类型
        if (!"RECHARGE_ONLY".equals(config.getRefundType()) && 
            !"PROPORTIONAL".equals(config.getRefundType()) && 
            !"FULL_REFUND".equals(config.getRefundType())) {
            throw new ServiceException("不支持的退款类型");
        }
        
        // 验证订单金额范围
        if (ObjectUtil.isNotNull(config.getMinOrderAmount()) && 
            config.getMinOrderAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("最小订单金额不能为负数");
        }
        
        if (ObjectUtil.isNotNull(config.getMaxOrderAmount()) && 
            config.getMaxOrderAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("最大订单金额必须大于0");
        }
        
        if (ObjectUtil.isNotNull(config.getMinOrderAmount()) && 
            ObjectUtil.isNotNull(config.getMaxOrderAmount()) &&
            config.getMinOrderAmount().compareTo(config.getMaxOrderAmount()) > 0) {
            throw new ServiceException("最小订单金额不能大于最大订单金额");
        }
        
        // 检查订单金额范围是否与其他规则冲突
        if (isOrderAmountRangeConflict(config)) {
            throw new ServiceException("订单金额范围与其他规则冲突");
        }
        
        // 检查规则名称是否重复
        if (isRuleNameExists(config.getRuleName(), config.getId())) {
            throw new ServiceException("规则名称已存在");
        }
    }

    /**
     * 检查规则名称是否已存在
     */
    private boolean isRuleNameExists(String ruleName, Long excludeId) {
        LambdaQueryWrapper<RefundRuleConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RefundRuleConfig::getRuleName, ruleName);
        if (ObjectUtil.isNotNull(excludeId)) {
            wrapper.ne(RefundRuleConfig::getId, excludeId);
        }
        return refundRuleConfigMapper.selectCount(wrapper) > 0;
    }

    /**
     * 检查订单金额范围是否与其他规则冲突
     */
    private boolean isOrderAmountRangeConflict(RefundRuleConfig config) {
        // 如果没有设置金额范围，不会冲突
        if (ObjectUtil.isNull(config.getMinOrderAmount()) && 
            ObjectUtil.isNull(config.getMaxOrderAmount())) {
            return false;
        }
        
        LambdaQueryWrapper<RefundRuleConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RefundRuleConfig::getStatus, "0"); // 只检查启用的规则
        if (ObjectUtil.isNotNull(config.getId())) {
            wrapper.ne(RefundRuleConfig::getId, config.getId());
        }
        
        List<RefundRuleConfig> existingRules = refundRuleConfigMapper.selectList(wrapper);
        
        for (RefundRuleConfig existingRule : existingRules) {
            if (isRangeOverlap(config.getMinOrderAmount(), config.getMaxOrderAmount(),
                              existingRule.getMinOrderAmount(), existingRule.getMaxOrderAmount())) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查两个金额范围是否重叠
     */
    private boolean isRangeOverlap(BigDecimal min1, BigDecimal max1, BigDecimal min2, BigDecimal max2) {
        // 处理null值，null表示无限制
        BigDecimal range1Min = min1 != null ? min1 : BigDecimal.ZERO;
        BigDecimal range1Max = max1 != null ? max1 : new BigDecimal("999999999");
        BigDecimal range2Min = min2 != null ? min2 : BigDecimal.ZERO;
        BigDecimal range2Max = max2 != null ? max2 : new BigDecimal("999999999");
        
        // 检查是否重叠
        return range1Min.compareTo(range2Max) <= 0 && range2Min.compareTo(range1Max) <= 0;
    }

    /**
     * 取消其他默认规则
     */
    private void clearOtherDefaultRules() {
        clearOtherDefaultRules(null);
    }

    /**
     * 取消其他默认规则
     */
    private void clearOtherDefaultRules(Long excludeId) {
        LambdaQueryWrapper<RefundRuleConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RefundRuleConfig::getIsDefault, "1");
        if (ObjectUtil.isNotNull(excludeId)) {
            wrapper.ne(RefundRuleConfig::getId, excludeId);
        }
        
        List<RefundRuleConfig> defaultRules = refundRuleConfigMapper.selectList(wrapper);
        for (RefundRuleConfig rule : defaultRules) {
            rule.setIsDefault("0");
            refundRuleConfigMapper.updateById(rule);
        }
    }

} 