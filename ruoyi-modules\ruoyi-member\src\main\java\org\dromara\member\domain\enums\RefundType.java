package org.dromara.member.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RefundType {

    /**
     * 只退充值金额
     */
    RECHARGE_ONLY("1", "只退充值金额"),

    /**
     * 按比例退款
     */
    RATIO_REFUND("2", "按比例退款"),

    /**
     * 全额退款（包含赠送）
     */
    FULL_REFUND("3", "全额退款（包含赠送）");

    private final String code;
    private final String desc;

    /**
     * 根据代码获取枚举
     */
    public static RefundType getByCode(String code) {
        for (RefundType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 