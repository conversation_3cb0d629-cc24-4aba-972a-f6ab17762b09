# 事务回滚异常修复说明

## 问题描述

在调用 `/member/walletAccount/consume` 接口时，出现以下异常：

```
org.springframework.transaction.UnexpectedRollbackException: Transaction rolled back because it has been marked as rollback-only
```

## 问题原因

这个异常是由于Spring事务管理的机制导致的：

1. **事务方法捕获异常但不重新抛出**：在带有 `@Transactional(rollbackFor = Exception.class)` 注解的方法中，当发生异常时，Spring会将事务标记为"仅回滚"状态。

2. **返回结果而非抛出异常**：如果在catch块中捕获异常后返回一个结果对象（而不是重新抛出异常），Spring会尝试提交事务。

3. **事务状态冲突**：当Spring尝试提交一个已被标记为"仅回滚"的事务时，就会抛出 `UnexpectedRollbackException`。

## 问题代码示例

```java
@Transactional(rollbackFor = Exception.class)
public WalletOperationResultVo consume(ConsumeRequestDto request) {
    try {
        // 业务逻辑
        if (!checkSufficientBalance(request.getMemberId(), request.getConsumeAmount())) {
            return buildFailureResult(...); // 问题：返回失败结果而不是抛出异常
        }
        // 其他业务逻辑...
        return result;
    } catch (Exception e) {
        log.error("消费失败", e);
        return buildFailureResult(...); // 问题：捕获异常后返回结果而不是重新抛出
    }
}
```

## 解决方案

将事务逻辑和异常处理逻辑分离：

### 方案1：分离事务方法（推荐）

```java
// 公共接口方法，负责异常处理
@Override
public WalletOperationResultVo consume(ConsumeRequestDto request) {
    try {
        return doConsume(request);
    } catch (Exception e) {
        log.error("消费失败", e);
        return buildFailureResult(request.getMemberId(), request.getOrderNo(),
            request.getConsumeAmount(), "消费失败：" + e.getMessage());
    }
}

// 私有事务方法，负责业务逻辑
@Transactional(rollbackFor = Exception.class)
private WalletOperationResultVo doConsume(ConsumeRequestDto request) {
    // 验证失败直接抛出异常
    if (!memberService.validateMemberStatus(request.getMemberId())) {
        throw new ServiceException("会员状态异常，无法消费");
    }
    
    if (!checkSufficientBalance(request.getMemberId(), request.getConsumeAmount())) {
        throw new ServiceException("余额不足");
    }
    
    // 其他业务逻辑...
    return result;
}
```

### 方案2：使用编程式事务

```java
@Autowired
private TransactionTemplate transactionTemplate;

@Override
public WalletOperationResultVo consume(ConsumeRequestDto request) {
    try {
        return transactionTemplate.execute(status -> {
            // 事务内的业务逻辑
            // 验证失败直接抛出异常，会自动回滚
            return doConsumeLogic(request);
        });
    } catch (Exception e) {
        log.error("消费失败", e);
        return buildFailureResult(...);
    }
}
```

## 修复的方法

本次修复涉及以下方法：

1. **WalletServiceImpl.consume()** - 消费方法
2. **WalletServiceImpl.refund()** - 退款方法

## 修复前后对比

### 修复前
- 事务方法直接处理异常并返回失败结果
- 导致事务状态冲突，抛出 `UnexpectedRollbackException`

### 修复后
- 将事务逻辑和异常处理分离
- 事务方法中的业务验证失败直接抛出异常
- 外层方法捕获异常并返回友好的失败结果
- 避免了事务状态冲突

## 最佳实践

1. **事务方法职责单一**：事务方法只负责业务逻辑，不处理异常转换
2. **异常处理外置**：在非事务方法中处理异常并转换为业务结果
3. **验证失败抛异常**：业务验证失败时抛出异常而不是返回失败结果
4. **明确事务边界**：清楚地定义哪些操作需要在事务中执行

## 测试验证

创建了专门的测试类 `WalletServiceTransactionTest` 来验证修复效果：

- 测试余额不足的消费请求
- 测试不存在订单的退款请求  
- 测试无效会员的操作请求

这些测试确保在各种异常情况下都不会抛出 `UnexpectedRollbackException`，而是正常返回失败结果。

## 注意事项

1. **保持API兼容性**：修复后的方法签名和返回值保持不变
2. **日志记录完整**：确保异常信息仍然被正确记录
3. **错误信息友好**：返回给调用方的错误信息要清晰明了
4. **性能影响最小**：修复方案不会显著影响性能
