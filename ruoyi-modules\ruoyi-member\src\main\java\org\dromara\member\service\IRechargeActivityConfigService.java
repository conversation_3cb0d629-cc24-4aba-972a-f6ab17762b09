package org.dromara.member.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.member.domain.RechargeActivityConfig;

import java.math.BigDecimal;
import java.util.List;

/**
 * 充值活动配置服务接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IRechargeActivityConfigService {

    /**
     * 分页查询充值活动配置
     */
    IPage<RechargeActivityConfig> selectPage(Page<RechargeActivityConfig> page, RechargeActivityConfig config);

    /**
     * 查询充值活动配置列表
     */
    List<RechargeActivityConfig> selectList(RechargeActivityConfig config);

    /**
     * 根据ID查询充值活动配置
     */
    RechargeActivityConfig selectById(Long id);

    /**
     * 新增充值活动配置
     */
    Boolean insert(RechargeActivityConfig config);

    /**
     * 修改充值活动配置
     */
    Boolean update(RechargeActivityConfig config);

    /**
     * 删除充值活动配置
     */
    Boolean deleteById(Long id);

    /**
     * 批量删除充值活动配置
     */
    Boolean deleteBatch(List<Long> ids);

    /**
     * 查询有效的充值活动配置
     */
    List<RechargeActivityConfig> selectActiveConfigs();

    /**
     * 根据充值金额匹配最佳活动
     */
    RechargeActivityConfig selectBestMatchActivity(BigDecimal amount);

    /**
     * 修改状态
     */
    Boolean changeStatus(Long id, String status);

} 