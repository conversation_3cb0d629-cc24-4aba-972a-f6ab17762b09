package org.dromara.member.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 优惠计算结果对象
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class DiscountResult {

    /**
     * 原始金额
     */
    private BigDecimal originalAmount;

    /**
     * 总优惠金额
     */
    private BigDecimal totalDiscountAmount;

    /**
     * 最终金额
     */
    private BigDecimal finalAmount;

    /**
     * 使用的优惠规则列表
     */
    private List<UsedRule> usedRules;

    /**
     * 可用的优惠方案列表
     */
    private List<DiscountPlan> availablePlans;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 使用的规则信息
     */
    @Data
    public static class UsedRule {
        /**
         * 规则ID
         */
        private Long ruleId;

        /**
         * 规则名称
         */
        private String ruleName;

        /**
         * 规则类型
         */
        private String ruleType;

        /**
         * 优惠金额
         */
        private BigDecimal discountAmount;

        /**
         * 适用的商品信息
         */
        private String applicableProducts;
    }

    /**
     * 优惠方案
     */
    @Data
    public static class DiscountPlan {
        /**
         * 方案名称
         */
        private String planName;

        /**
         * 方案描述
         */
        private String planDescription;

        /**
         * 总优惠金额
         */
        private BigDecimal totalDiscountAmount;

        /**
         * 最终金额
         */
        private BigDecimal finalAmount;

        /**
         * 包含的规则列表
         */
        private List<UsedRule> rules;

        /**
         * 是否推荐方案
         */
        private Boolean recommended;
    }

    /**
     * 创建成功结果
     */
    public static DiscountResult success(BigDecimal originalAmount, BigDecimal totalDiscountAmount, 
                                       BigDecimal finalAmount, List<UsedRule> usedRules) {
        DiscountResult result = new DiscountResult();
        result.setOriginalAmount(originalAmount);
        result.setTotalDiscountAmount(totalDiscountAmount);
        result.setFinalAmount(finalAmount);
        result.setUsedRules(usedRules);
        result.setSuccess(true);
        return result;
    }

    /**
     * 创建失败结果
     */
    public static DiscountResult failure(String errorMessage) {
        DiscountResult result = new DiscountResult();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        return result;
    }

}
