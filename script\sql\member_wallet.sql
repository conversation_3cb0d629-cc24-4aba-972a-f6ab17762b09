-- 会员钱包系统相关表结构

-- 1. 会员基础信息表
CREATE TABLE `member_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '会员ID',
  `member_no` varchar(32) NOT NULL COMMENT '会员编号（唯一）',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `gender` char(1) DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `district` varchar(50) DEFAULT NULL COMMENT '区县',
  `address` varchar(255) DEFAULT NULL COMMENT '详细地址',
  `password` varchar(100) DEFAULT NULL COMMENT '登录密码',
  `salt` varchar(50) DEFAULT NULL COMMENT '密码盐值',
  `register_channel` varchar(20) DEFAULT 'APP' COMMENT '注册渠道：APP, WEB, WECHAT, ALIPAY',
  `register_ip` varchar(50) DEFAULT NULL COMMENT '注册IP',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态：0-正常，1-禁用，2-注销',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_member_no` (`member_no`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员信息表';

-- 2. 会员统计表
CREATE TABLE `member_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `total_consume_amount` decimal(10,2) DEFAULT '0.00' COMMENT '累计消费金额',
  `total_consume_count` int(11) DEFAULT '0' COMMENT '累计消费次数',
  `total_recharge_amount` decimal(10,2) DEFAULT '0.00' COMMENT '累计充值金额',
  `total_recharge_count` int(11) DEFAULT '0' COMMENT '累计充值次数',
  `last_consume_time` datetime DEFAULT NULL COMMENT '最后消费时间',
  `last_recharge_time` datetime DEFAULT NULL COMMENT '最后充值时间',
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_member_id` (`member_id`),
  KEY `idx_total_consume_amount` (`total_consume_amount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员统计表';

-- 3. 会员钱包账户表
CREATE TABLE `member_wallet_account` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `total_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总余额',
  `recharge_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '充值余额（用户实际付费）',
  `bonus_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '赠送余额（平台赠送）',
  `frozen_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '冻结余额',
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_member_id` (`member_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员钱包账户表';

-- 4. 充值活动配置表
CREATE TABLE `recharge_activity_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `activity_name` varchar(100) NOT NULL COMMENT '活动名称',
  `recharge_amount` decimal(10,2) NOT NULL COMMENT '充值金额',
  `bonus_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '赠送金额',
  `start_time` datetime DEFAULT NULL COMMENT '活动开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '活动结束时间',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态：0-启用，1-禁用',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序（数字越大优先级越高）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status_sort` (`status`, `sort_order`),
  KEY `idx_time_range` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值活动配置表';

-- 5. 钱包流水记录表
CREATE TABLE `member_wallet_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `order_no` varchar(64) DEFAULT NULL COMMENT '关联订单号',
  `business_type` char(1) NOT NULL COMMENT '业务类型：1-充值，2-消费，3-退款，4-转账，5-活动赠送，6-管理员调整',
  `amount_type` char(1) NOT NULL COMMENT '金额类型：1-充值金额，2-赠送金额',
  `change_amount` decimal(10,2) NOT NULL COMMENT '变动金额（正数为增加，负数为减少）',
  `balance_before` decimal(10,2) NOT NULL COMMENT '变动前余额',
  `balance_after` decimal(10,2) NOT NULL COMMENT '变动后余额',
  `recharge_balance_before` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变动前充值余额',
  `recharge_balance_after` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变动后充值余额',
  `bonus_balance_before` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变动前赠送余额',
  `bonus_balance_after` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变动后赠送余额',
  `activity_id` bigint(20) DEFAULT NULL COMMENT '关联活动ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='钱包流水记录表';

-- 6. 消费策略配置表
CREATE TABLE `consume_strategy_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `strategy_name` varchar(100) NOT NULL COMMENT '策略名称',
  `strategy_type` char(1) NOT NULL COMMENT '策略类型：1-优先扣除充值金额，2-优先扣除赠送金额，3-按比例扣除',
  `recharge_ratio` decimal(5,2) DEFAULT NULL COMMENT '充值金额扣除比例（0-100）',
  `bonus_ratio` decimal(5,2) DEFAULT NULL COMMENT '赠送金额扣除比例（0-100）',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态：0-启用，1-禁用',
  `is_default` char(1) NOT NULL DEFAULT '0' COMMENT '是否默认策略：0-否，1-是',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消费策略配置表';

-- 7. 退款规则配置表
CREATE TABLE `refund_rule_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `refund_type` char(1) NOT NULL COMMENT '退款类型：1-只退充值金额，2-按比例退款，3-全额退款（包含赠送）',
  `bonus_refund_ratio` decimal(5,2) DEFAULT '0.00' COMMENT '赠送金额退款比例（0-100）',
  `min_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最小订单金额',
  `max_order_amount` decimal(10,2) DEFAULT NULL COMMENT '最大订单金额',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态：0-启用，1-禁用',
  `is_default` char(1) NOT NULL DEFAULT '0' COMMENT '是否默认规则：0-否，1-是',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款规则配置表';

-- 插入默认配置数据

-- 充值活动配置
INSERT INTO `recharge_activity_config` (`activity_name`, `recharge_amount`, `bonus_amount`, `status`, `sort_order`, `remark`) VALUES
('充值100送20', 100.00, 20.00, '0', 1, '充值满100元赠送20元'),
('充值200送40', 200.00, 40.00, '0', 2, '充值满200元赠送40元'),
('充值500送120', 500.00, 120.00, '0', 3, '充值满500元赠送120元');

-- 消费策略配置
INSERT INTO `consume_strategy_config` (`strategy_name`, `strategy_type`, `recharge_ratio`, `bonus_ratio`, `status`, `is_default`, `remark`) VALUES
('优先扣除赠送金额', '2', NULL, NULL, '0', '1', '消费时优先扣除赠送金额，赠送金额不足时扣除充值金额'),
('优先扣除充值金额', '1', NULL, NULL, '0', '0', '消费时优先扣除充值金额，充值金额不足时扣除赠送金额'),
('按比例扣除(1:1)', '3', 50.00, 50.00, '0', '0', '消费时按1:1比例同时扣除充值金额和赠送金额');

-- 退款规则配置
INSERT INTO `refund_rule_config` (`rule_name`, `refund_type`, `bonus_refund_ratio`, `status`, `is_default`, `remark`) VALUES
('只退充值金额', '1', 0.00, '0', '1', '退款时只退还用户实际支付的充值金额，不退赠送金额'),
('按比例退款', '2', 50.00, '0', '0', '退款时按消费比例退还充值和赠送金额'),
('全额退款', '3', 100.00, '0', '0', '退款时退还全部金额，包括赠送金额');
