package org.dromara.member.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 钱包业务类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum WalletBusinessType {

    /**
     * 充值
     */
    RECHARGE("1", "充值"),

    /**
     * 消费
     */
    CONSUME("2", "消费"),

    /**
     * 退款
     */
    REFUND("3", "退款"),

    /**
     * 转账
     */
    TRANSFER("4", "转账"),

    /**
     * 活动赠送
     */
    ACTIVITY_BONUS("5", "活动赠送"),

    /**
     * 管理员调整
     */
    ADMIN_ADJUST("6", "管理员调整");

    private final String code;
    private final String desc;

    /**
     * 根据代码获取枚举
     */
    public static WalletBusinessType getByCode(String code) {
        for (WalletBusinessType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 