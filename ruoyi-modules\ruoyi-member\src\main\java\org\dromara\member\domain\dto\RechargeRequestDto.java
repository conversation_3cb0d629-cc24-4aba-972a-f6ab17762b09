package org.dromara.member.domain.dto;

import lombok.Data;
import jakarta.validation.constraints.*;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 充值请求DTO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class RechargeRequestDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员ID
     */
    @NotNull(message = "会员ID不能为空")
    private Long memberId;

    /**
     * 充值金额
     */
    @NotNull(message = "充值金额不能为空")
    @DecimalMin(value = "0.01", message = "充值金额必须大于0")
    @DecimalMax(value = "99999.99", message = "充值金额不能超过99999.99")
    private BigDecimal rechargeAmount;

    /**
     * 支付方式
     */
    @NotBlank(message = "支付方式不能为空")
    private String paymentMethod;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 备注
     */
    private String remark;

} 