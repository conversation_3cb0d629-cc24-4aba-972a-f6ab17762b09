-- 积分功能表结构更新脚本
-- 用于为已存在的表添加缺失的字段

-- 检查并添加 points_rule 表的缺失字段
SET @sql = '';
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'points_rule' AND column_name = 'create_dept';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE points_rule ADD COLUMN create_dept bigint(20) DEFAULT NULL COMMENT ''创建部门'' AFTER remark;', 
    'SELECT ''Column create_dept already exists in points_rule'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 member_points_account 表的缺失字段
SET @sql = '';
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'member_points_account' AND column_name = 'create_dept';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE member_points_account ADD COLUMN create_dept bigint(20) DEFAULT NULL COMMENT ''创建部门'' AFTER version;', 
    'SELECT ''Column create_dept already exists in member_points_account'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 member_points_detail 表的缺失字段
SET @sql = '';
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'member_points_detail' AND column_name = 'create_dept';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE member_points_detail ADD COLUMN create_dept bigint(20) DEFAULT NULL COMMENT ''创建部门'' AFTER remark;', 
    'SELECT ''Column create_dept already exists in member_points_detail'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 如果表不存在，则创建完整的表结构
CREATE TABLE IF NOT EXISTS `points_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `min_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最小消费金额',
  `max_amount` decimal(10,2) DEFAULT NULL COMMENT '最大消费金额（null表示无上限）',
  `points_ratio` decimal(8,4) DEFAULT NULL COMMENT '积分比例（每元获得积分数）',
  `fixed_points` int(11) DEFAULT NULL COMMENT '固定积分数（优先级高于比例）',
  `start_time` datetime DEFAULT NULL COMMENT '规则生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '规则生效结束时间',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态：0-启用，1-禁用',
  `priority` int(11) NOT NULL DEFAULT '0' COMMENT '优先级（数字越大优先级越高）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_status_priority` (`status`, `priority`),
  KEY `idx_amount_range` (`min_amount`, `max_amount`),
  KEY `idx_time_range` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分规则配置表';

CREATE TABLE IF NOT EXISTS `member_points_account` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `total_points` bigint(20) NOT NULL DEFAULT '0' COMMENT '总积分',
  `available_points` bigint(20) NOT NULL DEFAULT '0' COMMENT '可用积分',
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_member_id` (`member_id`, `tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员积分账户表';

CREATE TABLE IF NOT EXISTS `member_points_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `order_no` varchar(64) DEFAULT NULL COMMENT '关联订单号',
  `business_type` char(1) NOT NULL DEFAULT '1' COMMENT '业务类型：1-消费获得，2-签到获得，3-活动获得，4-积分过期，5-管理员调整',
  `points_change` bigint(20) NOT NULL COMMENT '积分变动数量（正数为增加，负数为减少）',
  `points_before` bigint(20) NOT NULL COMMENT '变动前积分余额',
  `points_after` bigint(20) NOT NULL COMMENT '变动后积分余额',
  `rule_id` bigint(20) DEFAULT NULL COMMENT '关联规则ID',
  `consume_amount` decimal(10,2) DEFAULT NULL COMMENT '消费金额',
  `expire_time` datetime DEFAULT NULL COMMENT '积分过期时间（可选）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分明细记录表';

-- 插入示例积分规则数据（如果不存在）
INSERT IGNORE INTO `points_rule` (`rule_name`, `min_amount`, `max_amount`, `fixed_points`, `priority`, `status`, `remark`) VALUES
('满2元送3积分', 2.00, 4.99, 3, 1, '0', '消费满2元但不满5元时获得3积分'),
('满5元送10积分', 5.00, 9.99, 10, 2, '0', '消费满5元但不满10元时获得10积分'),
('满10元按1:1比例送积分', 10.00, NULL, NULL, 3, '0', '消费满10元时按1元=1积分的比例获得积分');

-- 更新积分规则表，设置积分比例
UPDATE `points_rule` SET `points_ratio` = 1.0000 WHERE `rule_name` = '满10元按1:1比例送积分' AND `points_ratio` IS NULL;
