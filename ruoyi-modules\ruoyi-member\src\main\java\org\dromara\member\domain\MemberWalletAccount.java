package org.dromara.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.member.domain.base.SimpleBaseEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 会员钱包账户对象 member_wallet_account
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("member_wallet_account")
public class MemberWalletAccount extends SimpleBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 总余额
     */
    private BigDecimal totalBalance;

    /**
     * 充值余额（用户实际付费）
     */
    private BigDecimal rechargeBalance;

    /**
     * 赠送余额（平台赠送）
     */
    private BigDecimal bonusBalance;

    /**
     * 冻结余额
     */
    private BigDecimal frozenBalance;

    /**
     * 乐观锁版本号
     */
    @Version
    private Integer version;

} 