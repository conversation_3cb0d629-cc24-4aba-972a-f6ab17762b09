package org.dromara.member.domain.bo;

import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.member.domain.MemberPointsAccount;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 会员积分账户业务对象 member_points_account
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MemberPointsAccount.class, reverseConvertGenerate = false)
public class MemberPointsAccountBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 会员ID
     */
    @NotNull(message = "会员ID不能为空")
    private Long memberId;

    /**
     * 总积分
     */
    @Min(value = 0, message = "总积分不能小于0")
    private Long totalPoints;

    /**
     * 可用积分
     */
    @Min(value = 0, message = "可用积分不能小于0")
    private Long availablePoints;

    /**
     * 乐观锁版本号
     */
    private Integer version;

}
