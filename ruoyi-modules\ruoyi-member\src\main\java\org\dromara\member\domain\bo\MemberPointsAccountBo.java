package org.dromara.member.domain.bo;

import org.dromara.member.domain.MemberPointsAccount;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会员积分账户业务对象 member_points_account
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MemberPointsAccount.class, reverseConvertGenerate = false)
public class MemberPointsAccountBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 总积分
     */
    private Long totalPoints;

    /**
     * 可用积分
     */
    private Long availablePoints;


}
