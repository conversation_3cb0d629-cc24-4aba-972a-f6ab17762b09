<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.member.mapper.MemberWalletAccountMapper">

    <resultMap type="org.dromara.member.domain.MemberWalletAccount" id="MemberWalletAccountResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="totalBalance"    column="total_balance"    />
        <result property="rechargeBalance"    column="recharge_balance"    />
        <result property="bonusBalance"    column="bonus_balance"    />
        <result property="frozenBalance"    column="frozen_balance"    />
        <result property="version"    column="version"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!-- 使用乐观锁更新钱包余额（充值） -->
    <update id="updateBalanceForRecharge">
        UPDATE member_wallet_account SET
        recharge_balance = recharge_balance + #{rechargeAmount},
        bonus_balance = bonus_balance + #{bonusAmount},
        total_balance = total_balance + #{totalAmount},
        version = version + 1,
        update_time = NOW()
        WHERE member_id = #{memberId} AND version = #{version}
    </update>

    <!-- 使用乐观锁更新钱包余额（消费） -->
    <update id="updateBalanceForConsume">
        UPDATE member_wallet_account SET
        recharge_balance = recharge_balance - #{rechargeDeduct},
        bonus_balance = bonus_balance - #{bonusDeduct},
        total_balance = total_balance - #{totalDeduct},
        version = version + 1,
        update_time = NOW()
        WHERE member_id = #{memberId} AND version = #{version}
    </update>

    <!-- 使用乐观锁更新冻结余额 -->
    <update id="updateFrozenBalance">
        UPDATE member_wallet_account SET
        frozen_balance = frozen_balance + #{amount},
        total_balance = total_balance - #{amount},
        version = version + 1,
        update_time = NOW()
        WHERE member_id = #{memberId} AND version = #{version}
    </update>

</mapper> 