package org.dromara.member.domain.bo;

import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.member.domain.PointsRule;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;

import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 积分规则配置业务对象 points_rule
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PointsRule.class, reverseConvertGenerate = false)
public class PointsRuleBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 规则名称
     */
    @NotBlank(message = "规则名称不能为空")
    @Size(max = 100, message = "规则名称长度不能超过{max}个字符")
    private String ruleName;

    /**
     * 最小消费金额
     */
    @NotNull(message = "最小消费金额不能为空")
    @DecimalMin(value = "0.00", message = "最小消费金额不能小于0")
    private BigDecimal minAmount;

    /**
     * 最大消费金额（null表示无上限）
     */
    @DecimalMin(value = "0.01", message = "最大消费金额必须大于0")
    private BigDecimal maxAmount;

    /**
     * 积分比例（每元获得积分数）
     */
    @DecimalMin(value = "0.0001", message = "积分比例必须大于0")
    private BigDecimal pointsRatio;

    /**
     * 固定积分数（优先级高于比例）
     */
    @Min(value = 1, message = "固定积分数必须大于0")
    private Integer fixedPoints;

    /**
     * 规则生效开始时间
     */
    private Date startTime;

    /**
     * 规则生效结束时间
     */
    private Date endTime;

    /**
     * 状态：0-启用，1-禁用
     */
    @NotBlank(message = "状态不能为空")
    private String status;

    /**
     * 优先级（数字越大优先级越高）
     */
    @NotNull(message = "优先级不能为空")
    @Min(value = 0, message = "优先级不能小于0")
    private Integer priority;

    /**
     * 备注说明
     */
    @Size(max = 500, message = "备注说明长度不能超过{max}个字符")
    private String remark;

}
