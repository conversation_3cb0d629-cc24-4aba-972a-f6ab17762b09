package org.dromara.member.domain.bo;

import org.dromara.member.domain.PointsRule;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;

/**
 * 积分规则配置业务对象 points_rule
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PointsRule.class, reverseConvertGenerate = false)
public class PointsRuleBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 最小消费金额
     */
    private Long minAmount;

    /**
     * 最大消费金额（null表示无上限）
     */
    @NotNull(message = "最大消费金额（null表示无上限）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long maxAmount;

    /**
     * 积分比例（每元获得积分数）
     */
    @NotNull(message = "积分比例（每元获得积分数）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long pointsRatio;

    /**
     * 固定积分数（优先级高于比例）
     */
    @NotNull(message = "固定积分数（优先级高于比例）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long fixedPoints;

    /**
     * 规则生效开始时间
     */
    @NotNull(message = "规则生效开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date startTime;

    /**
     * 规则生效结束时间
     */
    @NotNull(message = "规则生效结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date endTime;

    /**
     * 状态：0-禁用，1-启用
     */
    private Long status;

    /**
     * 优先级（数字越大优先级越高）
     */
    private Long priority;

    /**
     * 备注说明
     */
    @NotBlank(message = "备注说明不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
