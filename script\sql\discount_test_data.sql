-- 优惠规则和阶梯配置测试数据

-- 清理现有测试数据
DELETE FROM discount_ladder WHERE rule_id IN (SELECT id FROM discount_rule WHERE rule_name LIKE 'TEST_%');
DELETE FROM discount_rule WHERE rule_name LIKE 'TEST_%';

-- ========== discount_rule 测试数据 ==========

-- 1. 满减优惠规则
INSERT INTO discount_rule (
    rule_name, rule_type, min_amount, max_amount, discount_value, 
    status, priority, can_stack, user_limit_type, user_limit_count, total_limit_count,
    start_time, end_time, remark, create_time, tenant_id
) VALUES 
-- 满100减20
('TEST_满100减20', '1', 100.00, 199.99, 20.00, '0', 10, '0', '0', NULL, NULL, 
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '消费满100元减20元', NOW(), '000000'),

-- 满200减50
('TEST_满200减50', '1', 200.00, 299.99, 50.00, '0', 20, '0', '0', NULL, NULL,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '消费满200元减50元', NOW(), '000000'),

-- 满500减120
('TEST_满500减120', '1', 500.00, 999.99, 120.00, '0', 30, '0', '0', NULL, 1000,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '消费满500元减120元，限量1000份', NOW(), '000000'),

-- 2. 折扣优惠规则
-- 全场8折，最高优惠100元
('TEST_全场8折', '2', 0.01, NULL, 0.80, '0', 15, '1', '0', NULL, NULL,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '全场商品8折优惠，最高优惠100元，可叠加', NOW(), '000000'),

-- 新用户专享9折
('TEST_新用户9折', '2', 0.01, NULL, 0.90, '0', 25, '1', '2', 1, NULL,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '新用户专享9折优惠，每人限用1次', NOW(), '000000'),

-- VIP会员85折
('TEST_VIP会员85折', '2', 100.00, NULL, 0.85, '0', 35, '1', '1', 5, NULL,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', 'VIP会员85折优惠，每人限用5次', NOW(), '000000'),

-- 3. 阶梯满减规则
-- 基础阶梯满减
('TEST_基础阶梯满减', '4', 100.00, NULL, 0.00, '0', 40, '0', '0', NULL, NULL,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '阶梯式满减优惠：满100减15，满200减40，满300减80，满500减150', NOW(), '000000'),

-- 高级阶梯满减
('TEST_高级阶梯满减', '4', 200.00, NULL, 0.00, '0', 50, '0', '1', 3, NULL,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '高级阶梯满减：满200减30，满400减80，满600减150，满1000减300，每人限用3次', NOW(), '000000'),

-- 4. 特殊优惠规则
-- 限时抢购
('TEST_限时抢购7折', '2', 50.00, 500.00, 0.70, '0', 60, '0', '0', NULL, 100,
 '2024-06-01 10:00:00', '2024-06-01 18:00:00', '限时抢购7折优惠，限量100份，仅限6月1日', NOW(), '000000'),

-- 周末特惠
('TEST_周末满150减30', '1', 150.00, 299.99, 30.00, '0', 18, '0', '1', 2, NULL,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '周末特惠满150减30，每人限用2次', NOW(), '000000'),

-- 已禁用的规则（用于测试）
('TEST_已禁用规则', '1', 100.00, NULL, 25.00, '1', 5, '0', '0', NULL, NULL,
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', '已禁用的测试规则', NOW(), '000000');

-- 更新最大优惠金额（折扣规则）
UPDATE discount_rule SET max_discount_amount = 100.00 WHERE rule_name = 'TEST_全场8折';
UPDATE discount_rule SET max_discount_amount = 50.00 WHERE rule_name = 'TEST_新用户9折';
UPDATE discount_rule SET max_discount_amount = 200.00 WHERE rule_name = 'TEST_VIP会员85折';
UPDATE discount_rule SET max_discount_amount = 150.00 WHERE rule_name = 'TEST_限时抢购7折';

-- ========== discount_ladder 测试数据 ==========

-- 获取阶梯规则的ID
SET @basic_ladder_id = (SELECT id FROM discount_rule WHERE rule_name = 'TEST_基础阶梯满减');
SET @advanced_ladder_id = (SELECT id FROM discount_rule WHERE rule_name = 'TEST_高级阶梯满减');

-- 基础阶梯满减配置
INSERT INTO discount_ladder (rule_id, min_amount, max_amount, discount_value, sort_order, create_time, tenant_id) VALUES
-- 满100减15
(@basic_ladder_id, 100.00, 199.99, 15.00, 1, NOW(), '000000'),
-- 满200减40  
(@basic_ladder_id, 200.00, 299.99, 40.00, 2, NOW(), '000000'),
-- 满300减80
(@basic_ladder_id, 300.00, 499.99, 80.00, 3, NOW(), '000000'),
-- 满500减150
(@basic_ladder_id, 500.00, NULL, 150.00, 4, NOW(), '000000');

-- 高级阶梯满减配置
INSERT INTO discount_ladder (rule_id, min_amount, max_amount, discount_value, sort_order, create_time, tenant_id) VALUES
-- 满200减30
(@advanced_ladder_id, 200.00, 399.99, 30.00, 1, NOW(), '000000'),
-- 满400减80
(@advanced_ladder_id, 400.00, 599.99, 80.00, 2, NOW(), '000000'),
-- 满600减150
(@advanced_ladder_id, 600.00, 999.99, 150.00, 3, NOW(), '000000'),
-- 满1000减300
(@advanced_ladder_id, 1000.00, NULL, 300.00, 4, NOW(), '000000');

-- ========== 查询测试数据 ==========

-- 查看所有测试优惠规则
SELECT 
    '=== 优惠规则测试数据 ===' as info,
    '' as id, '' as rule_name, '' as rule_type, '' as min_amount, '' as max_amount, 
    '' as discount_value, '' as max_discount_amount, '' as status, '' as priority, '' as can_stack;

SELECT 
    id, rule_name, 
    CASE rule_type 
        WHEN '1' THEN '满减' 
        WHEN '2' THEN '折扣' 
        WHEN '4' THEN '阶梯满减' 
        ELSE rule_type 
    END as rule_type,
    min_amount, max_amount, discount_value, max_discount_amount,
    CASE status WHEN '0' THEN '启用' WHEN '1' THEN '禁用' END as status,
    priority, 
    CASE can_stack WHEN '0' THEN '不可叠加' WHEN '1' THEN '可叠加' END as can_stack
FROM discount_rule 
WHERE rule_name LIKE 'TEST_%'
ORDER BY priority DESC, id;

-- 查看阶梯配置
SELECT 
    '=== 阶梯配置测试数据 ===' as info,
    '' as rule_name, '' as min_amount, '' as max_amount, '' as discount_value, '' as sort_order;

SELECT 
    dr.rule_name,
    dl.min_amount,
    IFNULL(dl.max_amount, '无上限') as max_amount,
    dl.discount_value,
    dl.sort_order
FROM discount_ladder dl
JOIN discount_rule dr ON dl.rule_id = dr.id
WHERE dr.rule_name LIKE 'TEST_%'
ORDER BY dr.rule_name, dl.sort_order;

-- 统计信息
SELECT 
    '=== 统计信息 ===' as info,
    '' as rule_type, '' as count;

SELECT 
    CASE rule_type 
        WHEN '1' THEN '满减规则' 
        WHEN '2' THEN '折扣规则' 
        WHEN '4' THEN '阶梯满减规则' 
    END as rule_type,
    COUNT(*) as count
FROM discount_rule 
WHERE rule_name LIKE 'TEST_%'
GROUP BY rule_type;

SELECT 
    '阶梯配置总数' as rule_type,
    COUNT(*) as count
FROM discount_ladder dl
JOIN discount_rule dr ON dl.rule_id = dr.id
WHERE dr.rule_name LIKE 'TEST_%';

-- ========== 测试场景说明 ==========
SELECT '=== 测试场景说明 ===' as info;
SELECT '消费金额：120元' as scenario, '预期匹配：满100减20，优惠20元，实付100元' as expected;
SELECT '消费金额：250元' as scenario, '预期匹配：满200减50，优惠50元，实付200元' as expected;
SELECT '消费金额：350元（阶梯）' as scenario, '预期匹配：基础阶梯满减80元，实付270元' as expected;
SELECT '消费金额：600元（高级阶梯）' as scenario, '预期匹配：高级阶梯满减150元，实付450元' as expected;
SELECT '消费金额：100元（新用户）' as scenario, '预期匹配：新用户9折，优惠10元，实付90元' as expected;
SELECT '消费金额：1000元（多规则）' as scenario, '预期匹配：多种规则可选，系统推荐最优方案' as expected;
