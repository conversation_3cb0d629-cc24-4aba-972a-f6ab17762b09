package org.dromara.member.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.member.domain.bo.DiscountRuleBo;
import org.dromara.member.domain.vo.DiscountRuleVo;
import org.dromara.member.service.IDiscountRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 优惠规则配置
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Validated
@RestController
@RequestMapping("/member/discountRule")
public class DiscountRuleController extends BaseController {

    @Autowired
    private IDiscountRuleService discountRuleService;

    /**
     * 查询优惠规则配置列表
     */
    @SaCheckPermission("member:discountRule:list")
    @GetMapping("/list")
    public TableDataInfo<DiscountRuleVo> list(DiscountRuleBo bo, PageQuery pageQuery) {
        return discountRuleService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出优惠规则配置列表
     */
    @SaCheckPermission("member:discountRule:export")
    @Log(title = "优惠规则配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(DiscountRuleBo bo, HttpServletResponse response) {
        List<DiscountRuleVo> list = discountRuleService.queryList(bo);
        ExcelUtil.exportExcel(list, "优惠规则配置", DiscountRuleVo.class, response);
    }

    /**
     * 获取优惠规则配置详细信息
     */
    @SaCheckPermission("member:discountRule:query")
    @GetMapping("/{id}")
    public R<DiscountRuleVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(discountRuleService.queryById(id));
    }

    /**
     * 新增优惠规则配置
     */
    @SaCheckPermission("member:discountRule:add")
    @Log(title = "优惠规则配置", businessType = BusinessType.INSERT)
    @RepeatSubmit(interval = 2, timeUnit = TimeUnit.SECONDS, message = "请勿重复提交")
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody DiscountRuleBo bo) {
        return toAjax(discountRuleService.insertByBo(bo));
    }

    /**
     * 修改优惠规则配置
     */
    @SaCheckPermission("member:discountRule:edit")
    @Log(title = "优惠规则配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit(interval = 2, timeUnit = TimeUnit.SECONDS, message = "请勿重复提交")
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody DiscountRuleBo bo) {
        return toAjax(discountRuleService.updateByBo(bo));
    }

    /**
     * 删除优惠规则配置
     */
    @SaCheckPermission("member:discountRule:remove")
    @Log(title = "优惠规则配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(discountRuleService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 获取有效的优惠规则列表
     */
    @SaCheckPermission("member:discountRule:query")
    @GetMapping("/active")
    public R<List<DiscountRuleVo>> getActiveRules() {
        List<DiscountRuleVo> rules = discountRuleService.getActiveRules();
        return R.ok(rules);
    }

    /**
     * 根据金额获取适用的优惠规则
     */
    @SaCheckPermission("member:discountRule:query")
    @GetMapping("/applicable")
    public R<List<DiscountRuleVo>> getApplicableRules(
            @RequestParam @NotNull(message = "金额不能为空") BigDecimal amount,
            @RequestParam @NotNull(message = "会员ID不能为空") Long memberId,
            @RequestParam(required = false, defaultValue = "false") Boolean isNewUser) {
        List<DiscountRuleVo> rules = discountRuleService.getApplicableRules(amount, memberId, isNewUser);
        return R.ok(rules);
    }

}
