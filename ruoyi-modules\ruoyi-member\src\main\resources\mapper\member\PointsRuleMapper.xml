<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.member.mapper.PointsRuleMapper">

    <resultMap type="org.dromara.member.domain.vo.PointsRuleVo" id="PointsRuleResult">
        <result property="id"    column="id"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="minAmount"    column="min_amount"    />
        <result property="maxAmount"    column="max_amount"    />
        <result property="pointsRatio"    column="points_ratio"    />
        <result property="fixedPoints"    column="fixed_points"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="status"    column="status"    />
        <result property="priority"    column="priority"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!-- 查询有效的积分规则列表（明确指定字段避免create_dept字段错误） -->
    <select id="selectActiveRules" resultMap="PointsRuleResult">
        SELECT id, rule_name, min_amount, max_amount, points_ratio, fixed_points,
        start_time, end_time, status, priority, remark,
        create_by, create_time, update_by, update_time
        FROM points_rule
        WHERE status = #{status}
        AND (start_time IS NULL OR start_time &lt;= #{currentTime})
        AND (end_time IS NULL OR end_time &gt;= #{currentTime})
        ORDER BY priority DESC
    </select>

</mapper>
