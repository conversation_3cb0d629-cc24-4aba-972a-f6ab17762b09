package org.dromara.member.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.member.domain.MemberPointsAccount;
import org.dromara.member.domain.bo.MemberPointsAccountBo;
import org.dromara.member.domain.bo.MemberPointsDetailBo;
import org.dromara.member.domain.vo.MemberInfoVo;
import org.dromara.member.domain.vo.MemberPointsAccountVo;
import org.dromara.member.domain.vo.PointsRuleVo;
import org.dromara.member.mapper.MemberInfoMapper;
import org.dromara.member.mapper.MemberPointsAccountMapper;
import org.dromara.member.service.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 会员积分账户Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MemberPointsAccountServiceImpl implements IMemberPointsAccountService {

    private final MemberPointsAccountMapper baseMapper;
    private final IMemberPointsDetailService memberPointsDetailService;
    private final IPointsRuleService pointsRuleService;
    private final IPointsCacheService pointsCacheService;
    private final MemberInfoMapper memberInfoMapper;

    /**
     * 查询会员积分账户
     */
    @Override
    public MemberPointsAccountVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 根据会员ID查询积分账户
     */
    @Override
    public MemberPointsAccountVo queryByMemberId(Long memberId) {
        // 先从缓存获取
        MemberPointsAccountVo cachedAccount = pointsCacheService.getCachedMemberPointsAccount(memberId);
        if (cachedAccount != null) {
            return cachedAccount;
        }

        // 缓存未命中，从数据库查询
        LambdaQueryWrapper<MemberPointsAccount> lqw = Wrappers.lambdaQuery();
        lqw.eq(MemberPointsAccount::getMemberId, memberId);
        MemberPointsAccountVo account = baseMapper.selectVoOne(lqw);
        account.setMemberName(memberInfoMapper.selectVoById(memberId).getNickname());
        // 缓存查询结果
        if (account != null) {
            pointsCacheService.cacheMemberPointsAccount(memberId, account);
        }

        return account;
    }

    /**
     * 查询会员积分账户列表
     */
    @Override
    public TableDataInfo<MemberPointsAccountVo> queryPageList(MemberPointsAccountBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MemberPointsAccount> lqw = buildQueryWrapper(bo);
        Page<MemberPointsAccountVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        result.getRecords().forEach(item -> {
            MemberInfoVo member = memberInfoMapper.selectVoById(item.getMemberId());

            item.setMemberName(member != null ? member.getNickname() : "未知用户");
        });
        return TableDataInfo.build(result);
    }

    /**
     * 查询会员积分账户列表
     */
    @Override
    public List<MemberPointsAccountVo> queryList(MemberPointsAccountBo bo) {
        LambdaQueryWrapper<MemberPointsAccount> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MemberPointsAccount> buildQueryWrapper(MemberPointsAccountBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MemberPointsAccount> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getMemberId() != null, MemberPointsAccount::getMemberId, bo.getMemberId());
        lqw.eq(bo.getTotalPoints() != null, MemberPointsAccount::getTotalPoints, bo.getTotalPoints());
        lqw.eq(bo.getAvailablePoints() != null, MemberPointsAccount::getAvailablePoints, bo.getAvailablePoints());
        return lqw;
    }

    /**
     * 新增会员积分账户
     */
    @Override
    public Boolean insertByBo(MemberPointsAccountBo bo) {
        MemberPointsAccount add = MapstructUtils.convert(bo, MemberPointsAccount.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改会员积分账户
     */
    @Override
    public Boolean updateByBo(MemberPointsAccountBo bo) {
        MemberPointsAccount update = MapstructUtils.convert(bo, MemberPointsAccount.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MemberPointsAccount entity){
        // 校验积分数据
        if (entity.getTotalPoints() != null && entity.getTotalPoints() < 0) {
            throw new RuntimeException("总积分不能小于0");
        }
        if (entity.getAvailablePoints() != null && entity.getAvailablePoints() < 0) {
            throw new RuntimeException("可用积分不能小于0");
        }
        if (entity.getTotalPoints() != null && entity.getAvailablePoints() != null
            && entity.getAvailablePoints() > entity.getTotalPoints()) {
            throw new RuntimeException("可用积分不能大于总积分");
        }
    }

    /**
     * 批量删除会员积分账户
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 为会员增加积分
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addPoints(Long memberId, Long points, String orderNo, BigDecimal amount) {
        if (points <= 0) {
            log.warn("积分数量必须大于0, memberId: {}, points: {}", memberId, points);
            return false;
        }

        // 查询或创建积分账户
        MemberPointsAccountVo account = queryByMemberId(memberId);
        if (account == null) {
            createAccount(memberId);
            account = queryByMemberId(memberId);
        }

        // 记录变动前的积分
        Long pointsBefore = account.getAvailablePoints();
        Long pointsAfter = pointsBefore + points;

        // 使用乐观锁更新积分账户
        LambdaUpdateWrapper<MemberPointsAccount> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(MemberPointsAccount::getMemberId, memberId);
        updateWrapper.eq(MemberPointsAccount::getVersion, account.getVersion());
        updateWrapper.set(MemberPointsAccount::getTotalPoints, account.getTotalPoints() + points);
        updateWrapper.set(MemberPointsAccount::getAvailablePoints, pointsAfter);

        boolean updateResult = baseMapper.update(null, updateWrapper) > 0;
        if (!updateResult) {
            log.error("更新积分账户失败，可能存在并发问题, memberId: {}", memberId);
            throw new RuntimeException("更新积分账户失败");
        }

        // 清除相关缓存
        pointsCacheService.clearMemberPointsAccountCache(memberId);
        pointsCacheService.clearMemberPointsBalanceCache(memberId);

        // 记录积分明细
        MemberPointsDetailBo detailBo = new MemberPointsDetailBo();
        detailBo.setMemberId(memberId);
        detailBo.setOrderNo(orderNo);
        detailBo.setBusinessType(1); // 消费获得
        detailBo.setPointsChange(points);
        detailBo.setPointsBefore(pointsBefore);
        detailBo.setPointsAfter(pointsAfter);
        detailBo.setConsumeAmount(amount);
        detailBo.setRemark("消费获得积分");

        // 查找使用的规则
        if (amount != null) {
            PointsRuleVo rule = pointsRuleService.findBestRule(amount);
            if (rule != null) {
                detailBo.setRuleId(rule.getId());
            }
        }

        return memberPointsDetailService.recordPointsChange(detailBo);
    }

    /**
     * 创建积分账户
     */
    @Override
    public Boolean createAccount(Long memberId) {
        // 检查账户是否已存在
        MemberPointsAccountVo existAccount = queryByMemberId(memberId);
        if (existAccount != null) {
            return true;
        }

        MemberPointsAccount account = new MemberPointsAccount();
        account.setMemberId(memberId);
        account.setTotalPoints(0L);
        account.setAvailablePoints(0L);
        account.setVersion(0);

        return baseMapper.insert(account) > 0;
    }

    /**
     * 获取会员积分余额
     */
    @Override
    public Long getMemberPoints(Long memberId) {
        // 先从缓存获取
        Long cachedBalance = pointsCacheService.getCachedMemberPointsBalance(memberId);
        if (cachedBalance != null) {
            return cachedBalance;
        }

        // 缓存未命中，从数据库查询
        MemberPointsAccountVo account = queryByMemberId(memberId);
        Long balance = account != null ? account.getAvailablePoints() : 0L;

        // 缓存查询结果
        pointsCacheService.cacheMemberPointsBalance(memberId, balance);

        return balance;
    }
}
