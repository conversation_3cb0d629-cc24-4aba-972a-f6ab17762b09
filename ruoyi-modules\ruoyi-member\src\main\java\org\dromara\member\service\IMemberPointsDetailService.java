package org.dromara.member.service;

import org.dromara.member.domain.vo.MemberPointsDetailVo;
import org.dromara.member.domain.bo.MemberPointsDetailBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 积分明细记录Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IMemberPointsDetailService {

    /**
     * 查询积分明细记录
     */
    MemberPointsDetailVo queryById(Long id);

    /**
     * 查询积分明细记录列表
     */
    TableDataInfo<MemberPointsDetailVo> queryPageList(MemberPointsDetailBo bo, PageQuery pageQuery);

    /**
     * 查询积分明细记录列表
     */
    List<MemberPointsDetailVo> queryList(MemberPointsDetailBo bo);

    /**
     * 新增积分明细记录
     */
    Boolean insertByBo(MemberPointsDetailBo bo);

    /**
     * 修改积分明细记录
     */
    Boolean updateByBo(MemberPointsDetailBo bo);

    /**
     * 校验并批量删除积分明细记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 记录积分变动
     */
    Boolean recordPointsChange(MemberPointsDetailBo detailBo);

    /**
     * 获取会员积分历史
     */
    TableDataInfo<MemberPointsDetailVo> getMemberPointsHistory(Long memberId, PageQuery pageQuery);
}
