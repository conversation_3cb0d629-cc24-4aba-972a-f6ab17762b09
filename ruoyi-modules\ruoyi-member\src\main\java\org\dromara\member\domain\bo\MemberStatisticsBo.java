package org.dromara.member.domain.bo;

import org.dromara.member.domain.MemberStatistics;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;

/**
 * 会员统计业务对象 member_statistics
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MemberStatistics.class, reverseConvertGenerate = false)
public class MemberStatisticsBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 累计消费金额
     */
    @NotNull(message = "累计消费金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long totalConsumeAmount;

    /**
     * 累计消费次数
     */
    @NotNull(message = "累计消费次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long totalConsumeCount;

    /**
     * 累计充值金额
     */
    @NotNull(message = "累计充值金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long totalRechargeAmount;

    /**
     * 累计充值次数
     */
    @NotNull(message = "累计充值次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long totalRechargeCount;

    /**
     * 最后消费时间
     */
    @NotNull(message = "最后消费时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date lastConsumeTime;

    /**
     * 最后充值时间
     */
    @NotNull(message = "最后充值时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date lastRechargeTime;


}
