<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.member.mapper.RechargeActivityConfigMapper">

    <resultMap type="org.dromara.member.domain.RechargeActivityConfig" id="RechargeActivityConfigResult">
        <result property="id"    column="id"    />
        <result property="activityName"    column="activity_name"    />
        <result property="rechargeAmount"    column="recharge_amount"    />
        <result property="bonusAmount"    column="bonus_amount"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="description"    column="description"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!-- 查询有效的充值活动配置 -->
    <select id="selectActiveConfigs" resultMap="RechargeActivityConfigResult">
        SELECT * FROM recharge_activity_config
        WHERE status = '0'
        AND (start_time IS NULL OR start_time &lt;= NOW())
        AND (end_time IS NULL OR end_time &gt;= NOW())
        ORDER BY sort_order DESC, recharge_amount DESC
    </select>

    <!-- 根据充值金额匹配最佳活动 -->
    <select id="selectBestMatchActivity" resultMap="RechargeActivityConfigResult">
        SELECT * FROM recharge_activity_config
        WHERE status = '0'
        AND recharge_amount &lt;= #{amount}
        AND (start_time IS NULL OR start_time &lt;= NOW())
        AND (end_time IS NULL OR end_time &gt;= NOW())
        ORDER BY sort_order DESC, recharge_amount DESC
        LIMIT 1
    </select>

</mapper> 