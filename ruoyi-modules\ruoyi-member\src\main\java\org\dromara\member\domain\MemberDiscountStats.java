package org.dromara.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serial;
import java.util.Date;

/**
 * 会员优惠使用统计对象 member_discount_stats
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@TableName("member_discount_stats")
public class MemberDiscountStats {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 已使用次数
     */
    private Integer usedCount;

    /**
     * 最后使用时间
     */
    private Date lastUsedTime;

    /**
     * 乐观锁版本号
     */
    @Version
    private Integer version;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 租户编号
     */
    private String tenantId;

}
