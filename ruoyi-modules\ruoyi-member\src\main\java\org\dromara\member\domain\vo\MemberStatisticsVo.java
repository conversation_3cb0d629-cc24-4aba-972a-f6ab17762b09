package org.dromara.member.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.member.domain.MemberStatistics;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 会员统计视图对象 member_statistics
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MemberStatistics.class)
public class MemberStatisticsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 会员ID
     */
    @ExcelProperty(value = "会员ID")
    private Long memberId;

    /**
     * 累计消费金额
     */
    @ExcelProperty(value = "累计消费金额")
    private Long totalConsumeAmount;

    /**
     * 累计消费次数
     */
    @ExcelProperty(value = "累计消费次数")
    private Long totalConsumeCount;

    /**
     * 累计充值金额
     */
    @ExcelProperty(value = "累计充值金额")
    private Long totalRechargeAmount;

    /**
     * 累计充值次数
     */
    @ExcelProperty(value = "累计充值次数")
    private Long totalRechargeCount;

    /**
     * 最后消费时间
     */
    @ExcelProperty(value = "最后消费时间")
    private Date lastConsumeTime;

    /**
     * 最后充值时间
     */
    @ExcelProperty(value = "最后充值时间")
    private Date lastRechargeTime;


}
