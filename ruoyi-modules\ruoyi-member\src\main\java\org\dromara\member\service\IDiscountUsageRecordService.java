package org.dromara.member.service;

import org.dromara.member.domain.vo.DiscountUsageRecordVo;
import org.dromara.member.domain.bo.DiscountUsageRecordBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 优惠使用记录Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IDiscountUsageRecordService {

    /**
     * 查询优惠使用记录
     */
    DiscountUsageRecordVo queryById(Long id);

    /**
     * 查询优惠使用记录列表
     */
    TableDataInfo<DiscountUsageRecordVo> queryPageList(DiscountUsageRecordBo bo, PageQuery pageQuery);

    /**
     * 查询优惠使用记录列表
     */
    List<DiscountUsageRecordVo> queryList(DiscountUsageRecordBo bo);

    /**
     * 新增优惠使用记录
     */
    Boolean insertByBo(DiscountUsageRecordBo bo);

    /**
     * 修改优惠使用记录
     */
    Boolean updateByBo(DiscountUsageRecordBo bo);

    /**
     * 校验并批量删除优惠使用记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 记录优惠使用
     */
    Boolean recordUsage(DiscountUsageRecordBo recordBo);

    /**
     * 获取会员优惠使用历史
     */
    TableDataInfo<DiscountUsageRecordVo> getMemberUsageHistory(Long memberId, PageQuery pageQuery);

    /**
     * 根据订单号查询使用记录
     */
    List<DiscountUsageRecordVo> getByOrderNo(String orderNo);

    /**
     * 更新使用记录状态为已退款
     */
    Boolean updateStatusToRefunded(String orderNo);

}
