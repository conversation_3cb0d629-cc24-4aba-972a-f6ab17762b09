<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.member.mapper.MemberWalletDetailMapper">

    <resultMap type="org.dromara.member.domain.MemberWalletDetail" id="MemberWalletDetailResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="businessType"    column="business_type"    />
        <result property="amountType"    column="amount_type"    />
        <result property="changeAmount"    column="change_amount"    />
        <result property="balanceBefore"    column="balance_before"    />
        <result property="balanceAfter"    column="balance_after"    />
        <result property="rechargeBalanceBefore"    column="recharge_balance_before"    />
        <result property="rechargeBalanceAfter"    column="recharge_balance_after"    />
        <result property="bonusBalanceBefore"    column="bonus_balance_before"    />
        <result property="bonusBalanceAfter"    column="bonus_balance_after"    />
        <result property="activityId"    column="activity_id"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!-- 根据订单号查询流水记录 -->
    <select id="selectByOrderNo" resultMap="MemberWalletDetailResult">
        SELECT * FROM member_wallet_detail
        WHERE order_no = #{orderNo}
        ORDER BY create_time DESC
    </select>

    <!-- 根据会员ID查询最近的流水记录 -->
    <select id="selectRecentByMemberId" resultMap="MemberWalletDetailResult">
        SELECT * FROM member_wallet_detail
        WHERE member_id = #{memberId}
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 根据会员ID和业务类型查询流水记录 -->
    <select id="selectByMemberIdAndBusinessType" resultMap="MemberWalletDetailResult">
        SELECT * FROM member_wallet_detail
        WHERE member_id = #{memberId}
        AND business_type = #{businessType}
        ORDER BY create_time DESC
    </select>

</mapper> 