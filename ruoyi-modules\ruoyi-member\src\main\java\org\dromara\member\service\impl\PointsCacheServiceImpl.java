package org.dromara.member.service.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.member.constant.PointsCacheConstants;
import org.dromara.member.domain.vo.MemberPointsAccountVo;
import org.dromara.member.domain.vo.PointsRuleVo;
import org.dromara.member.service.IPointsCacheService;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;

/**
 * 积分缓存服务实现类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PointsCacheServiceImpl implements IPointsCacheService {

    /**
     * 缓存有效积分规则
     */
    @Override
    public void cacheActiveRules(List<PointsRuleVo> rules) {
        try {
            if (CollUtil.isNotEmpty(rules)) {
                RedisUtils.setCacheObject(
                    PointsCacheConstants.ACTIVE_POINTS_RULES_CACHE_KEY,
                    rules,
                    Duration.ofSeconds(PointsCacheConstants.RULES_CACHE_EXPIRE_TIME)
                );
                log.debug("缓存有效积分规则成功，规则数量：{}", rules.size());
            }
        } catch (Exception e) {
            log.error("缓存有效积分规则失败", e);
        }
    }

    /**
     * 获取缓存的有效积分规则
     */
    @Override
    @SuppressWarnings("unchecked")
    public List<PointsRuleVo> getCachedActiveRules() {
        try {
            Object cached = RedisUtils.getCacheObject(PointsCacheConstants.ACTIVE_POINTS_RULES_CACHE_KEY);
            if (cached instanceof List) {
                List<PointsRuleVo> rules = (List<PointsRuleVo>) cached;
                log.debug("从缓存获取有效积分规则成功，规则数量：{}", rules.size());
                return rules;
            }
        } catch (Exception e) {
            log.error("从缓存获取有效积分规则失败", e);
        }
        return null;
    }

    /**
     * 清除积分规则缓存
     */
    @Override
    public void clearRulesCache() {
        try {
            RedisUtils.deleteObject(PointsCacheConstants.ACTIVE_POINTS_RULES_CACHE_KEY);
            log.debug("清除积分规则缓存成功");
        } catch (Exception e) {
            log.error("清除积分规则缓存失败", e);
        }
    }

    /**
     * 缓存会员积分余额
     */
    @Override
    public void cacheMemberPointsBalance(Long memberId, Long balance) {
        try {
            String cacheKey = PointsCacheConstants.getMemberPointsBalanceCacheKey(memberId);
            RedisUtils.setCacheObject(
                cacheKey,
                balance,
                Duration.ofSeconds(PointsCacheConstants.BALANCE_CACHE_EXPIRE_TIME)
            );
            log.debug("缓存会员{}积分余额{}成功", memberId, balance);
        } catch (Exception e) {
            log.error("缓存会员积分余额失败，memberId：{}", memberId, e);
        }
    }

    /**
     * 获取缓存的会员积分余额
     */
    @Override
    public Long getCachedMemberPointsBalance(Long memberId) {
        try {
            String cacheKey = PointsCacheConstants.getMemberPointsBalanceCacheKey(memberId);
            Object cached = RedisUtils.getCacheObject(cacheKey);
            if (cached instanceof Long) {
                Long balance = (Long) cached;
                log.debug("从缓存获取会员{}积分余额{}成功", memberId, balance);
                return balance;
            }
        } catch (Exception e) {
            log.error("从缓存获取会员积分余额失败，memberId：{}", memberId, e);
        }
        return null;
    }

    /**
     * 清除会员积分余额缓存
     */
    @Override
    public void clearMemberPointsBalanceCache(Long memberId) {
        try {
            String cacheKey = PointsCacheConstants.getMemberPointsBalanceCacheKey(memberId);
            RedisUtils.deleteObject(cacheKey);
            log.debug("清除会员{}积分余额缓存成功", memberId);
        } catch (Exception e) {
            log.error("清除会员积分余额缓存失败，memberId：{}", memberId, e);
        }
    }

    /**
     * 缓存会员积分账户
     */
    @Override
    public void cacheMemberPointsAccount(Long memberId, MemberPointsAccountVo account) {
        try {
            String cacheKey = PointsCacheConstants.getMemberPointsAccountCacheKey(memberId);
            RedisUtils.setCacheObject(
                cacheKey,
                account,
                Duration.ofSeconds(PointsCacheConstants.CACHE_EXPIRE_TIME)
            );
            log.debug("缓存会员{}积分账户成功", memberId);
        } catch (Exception e) {
            log.error("缓存会员积分账户失败，memberId：{}", memberId, e);
        }
    }

    /**
     * 获取缓存的会员积分账户
     */
    @Override
    public MemberPointsAccountVo getCachedMemberPointsAccount(Long memberId) {
        try {
            String cacheKey = PointsCacheConstants.getMemberPointsAccountCacheKey(memberId);
            Object cached = RedisUtils.getCacheObject(cacheKey);
            if (cached instanceof MemberPointsAccountVo) {
                MemberPointsAccountVo account = (MemberPointsAccountVo) cached;
                log.debug("从缓存获取会员{}积分账户成功", memberId);
                return account;
            }
        } catch (Exception e) {
            log.error("从缓存获取会员积分账户失败，memberId：{}", memberId, e);
        }
        return null;
    }

    /**
     * 清除会员积分账户缓存
     */
    @Override
    public void clearMemberPointsAccountCache(Long memberId) {
        try {
            String cacheKey = PointsCacheConstants.getMemberPointsAccountCacheKey(memberId);
            RedisUtils.deleteObject(cacheKey);
            log.debug("清除会员{}积分账户缓存成功", memberId);
        } catch (Exception e) {
            log.error("清除会员积分账户缓存失败，memberId：{}", memberId, e);
        }
    }

    /**
     * 刷新所有缓存
     */
    @Override
    public void refreshAllCache() {
        try {
            // 清除所有积分相关缓存
            clearRulesCache();
            log.info("刷新所有积分缓存成功");
        } catch (Exception e) {
            log.error("刷新所有积分缓存失败", e);
        }
    }
}
