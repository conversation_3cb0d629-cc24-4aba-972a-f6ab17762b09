package org.dromara.member.service;

import org.dromara.member.domain.MemberInfo;
import org.dromara.member.domain.vo.WalletBalanceVo;

/**
 * 会员服务接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IMemberService {

    /**
     * 根据会员ID查询会员信息
     *
     * @param memberId 会员ID
     * @return 会员信息
     */
    MemberInfo getMemberById(Long memberId);

    /**
     * 根据会员编号查询会员信息
     *
     * @param memberNo 会员编号
     * @return 会员信息
     */
    MemberInfo getMemberByNo(String memberNo);

    /**
     * 根据手机号查询会员信息
     *
     * @param phone 手机号
     * @return 会员信息
     */
    MemberInfo getMemberByPhone(String phone);

    /**
     * 创建会员
     *
     * @param memberInfo 会员信息
     * @return 是否创建成功
     */
    Boolean createMember(MemberInfo memberInfo);

    /**
     * 更新会员信息
     *
     * @param memberInfo 会员信息
     * @return 是否更新成功
     */
    Boolean updateMember(MemberInfo memberInfo);

    /**
     * 验证会员状态
     *
     * @param memberId 会员ID
     * @return 是否可用
     */
    Boolean validateMemberStatus(Long memberId);

    /**
     * 获取会员钱包余额信息
     *
     * @param memberId 会员ID
     * @return 钱包余额信息
     */
    WalletBalanceVo getMemberWalletBalance(Long memberId);

    /**
     * 更新会员最后登录信息
     *
     * @param memberId 会员ID
     * @param loginIp 登录IP
     */
    void updateLastLoginInfo(Long memberId, String loginIp);

} 