package org.dromara.member.service;

import jakarta.validation.constraints.NotEmpty;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.member.domain.MemberInfo;
import org.dromara.member.domain.bo.MemberInfoBo;
import org.dromara.member.domain.vo.MemberInfoVo;
import org.dromara.member.domain.vo.WalletBalanceVo;

import java.util.List;

/**
 * 会员服务接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IMemberService {

    /**
     * 根据会员ID查询会员信息
     *
     * @param memberId 会员ID
     * @return 会员信息
     */
    MemberInfoVo getMemberById(Long memberId);

    /**
     * 根据会员编号查询会员信息
     *
     * @param memberNo 会员编号
     * @return 会员信息
     */
    MemberInfo getMemberByNo(String memberNo);

    /**
     * 根据手机号查询会员信息
     *
     * @param phone 手机号
     * @return 会员信息
     */
    MemberInfo getMemberByPhone(String phone);

    /**
     * 创建会员
     *
     * @param memberInfo 会员信息
     * @return 是否创建成功
     */
    Boolean createMember(MemberInfoBo memberInfo);

    /**
     * 更新会员信息
     *
     * @param memberInfo 会员信息
     * @return 是否更新成功
     */
    Boolean updateMember(MemberInfo memberInfo);

    /**
     * 验证会员状态
     *
     * @param memberId 会员ID
     * @return 是否可用
     */
    Boolean validateMemberStatus(Long memberId);

    /**
     * 获取会员钱包余额信息
     *
     * @param memberId 会员ID
     * @return 钱包余额信息
     */
    WalletBalanceVo getMemberWalletBalance(Long memberId);

    /**
     * 更新会员最后登录信息
     *
     * @param memberId 会员ID
     * @param loginIp 登录IP
     */
    void updateLastLoginInfo(Long memberId, String loginIp);

    /**
     * 分页查询会员信息列表
     */
    TableDataInfo<MemberInfoVo> queryPageList(MemberInfoBo bo, PageQuery pageQuery);

    /**
     * 查询会员信息列表
     */
    List<MemberInfoVo> queryList(MemberInfoBo bo);

    /**
     * 校验并批量删除会员信息
     */
    Boolean deleteWithValidByIds(List<Long> ids, boolean b);
}
