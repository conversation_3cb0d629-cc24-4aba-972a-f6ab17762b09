package org.dromara.member.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjectUtil;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.member.domain.MemberWalletDetail;
import org.dromara.member.domain.bo.MemberWalletDetailBo;
import org.dromara.member.domain.vo.MemberWalletDetailVo;
import org.dromara.member.service.IWalletDetailService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 钱包流水记录Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/member/walletDetail")
public class WalletDetailController extends BaseController {

    private final IWalletDetailService walletDetailService;

    /**
     * 查询钱包流水记录列表
     */
    @SaCheckPermission("member:walletDetail:list")
    @GetMapping("/list")
    public TableDataInfo<MemberWalletDetailVo> list(MemberWalletDetailBo bo, PageQuery pageQuery) {
        return walletDetailService.queryPageList(bo, pageQuery);
    }

    /**
     * 新增钱包流水记录
     */
    @SaCheckPermission("member:walletDetail:add")
    @Log(title = "钱包流水记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MemberWalletDetailBo bo) {
        return toAjax(walletDetailService.insertByBo(bo));
    }

    /**
     * 修改钱包流水记录
     */
    @SaCheckPermission("member:walletDetail:edit")
    @Log(title = "钱包流水记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MemberWalletDetailBo bo) {
        return toAjax(walletDetailService.updateByBo(bo));
    }

    /**
     * 删除钱包流水记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("member:walletDetail:remove")
    @Log(title = "钱包流水记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(walletDetailService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 根据会员ID查询钱包流水记录
     */
    @GetMapping("/member/{memberId}")
    @Log(title = "会员钱包流水")
    public R<List<MemberWalletDetail>> getByMemberId(@NotNull(message = "会员ID不能为空") @PathVariable Long memberId,
                                                     @RequestParam(required = false) String businessType,
                                                     @RequestParam(required = false, defaultValue = "10") Integer limit) {

        try {
            List<MemberWalletDetail> detailList;

            if (ObjectUtil.isNotEmpty(businessType)) {
                // 根据会员ID和业务类型查询
                detailList = walletDetailService.selectByMemberIdAndBusinessType(memberId, businessType);

                // 如果有限制数量，截取前N条
                if (limit > 0 && detailList.size() > limit) {
                    detailList = detailList.subList(0, limit);
                }
            } else {
                // 根据会员ID查询最近的流水记录
                detailList = walletDetailService.selectRecentByMemberId(memberId, limit);
            }

            log.info("查询会员钱包流水记录成功，会员ID：{}，业务类型：{}，数量：{}",
                    memberId, businessType, detailList.size());

            return R.ok(detailList);

        } catch (Exception e) {
            log.error("查询会员钱包流水记录失败，会员ID：{}", memberId, e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据订单号查询钱包流水记录
     */
    @GetMapping("/order/{orderNo}")
    @Log(title = "订单钱包流水")
    public R<List<MemberWalletDetail>> getByOrderNo(@PathVariable String orderNo) {

        try {
            List<MemberWalletDetail> detailList = walletDetailService.selectByOrderNo(orderNo);

            log.info("查询订单钱包流水记录成功，订单号：{}，数量：{}", orderNo, detailList.size());

            return R.ok(detailList);

        } catch (Exception e) {
            log.error("查询订单钱包流水记录失败，订单号：{}", orderNo, e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID获取钱包流水记录详情
     */
    @GetMapping("/{id}")
    @Log(title = "钱包流水详情")
    public R<MemberWalletDetailVo> getInfo(@NotNull(message = "ID不能为空") @PathVariable Long id) {

        try {
            MemberWalletDetailVo detail = walletDetailService.selectById(id);

            if (ObjectUtil.isNull(detail)) {
                return R.fail("钱包流水记录不存在");
            }

            log.info("查询钱包流水记录详情成功，ID：{}", id);

            return R.ok(detail);

        } catch (Exception e) {
            log.error("查询钱包流水记录详情失败，ID：{}", id, e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }


    /**
     * 统计会员钱包流水汇总信息
     */
    @GetMapping("/member/{memberId}/summary")
    @Log(title = "钱包流水汇总")
    public R<Map<String, Object>> getMemberWalletSummary(@NotNull(message = "会员ID不能为空") @PathVariable Long memberId,
                                            @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(required = false) LocalDate startDate,
                                            @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(required = false) LocalDate endDate) {

        try {
            // 构建查询条件
            MemberWalletDetailBo queryDetail = new MemberWalletDetailBo();
            queryDetail.setMemberId(memberId);

            // 处理日期范围
            if (ObjectUtil.isNotNull(startDate)) {
                Date startDateTime = Date.from(startDate.atStartOfDay().atZone(java.time.ZoneId.systemDefault()).toInstant());
                queryDetail.setCreateTime(startDateTime);
            }

            // 查询流水记录
            List<MemberWalletDetail> detailList = walletDetailService.selectList(queryDetail);

            // 统计汇总信息
            Map<String, Object> summary = calculateWalletSummary(detailList, startDate, endDate);

            log.info("统计会员钱包流水汇总成功，会员ID：{}，统计期间：{} - {}",
                    memberId, startDate, endDate);

            return R.ok(summary);

        } catch (Exception e) {
            log.error("统计会员钱包流水汇总失败，会员ID：{}", memberId, e);
            return R.fail("统计失败：" + e.getMessage());
        }
    }

    /**
     * 计算钱包流水汇总信息
     */
    private Map<String, Object> calculateWalletSummary(List<MemberWalletDetail> detailList, LocalDate startDate, LocalDate endDate) {
        Map<String, Object> summary = new HashMap<>();

        // 初始化统计数据
        BigDecimal totalRechargeAmount = BigDecimal.ZERO;     // 总充值金额
        BigDecimal totalConsumeAmount = BigDecimal.ZERO;      // 总消费金额
        BigDecimal totalRefundAmount = BigDecimal.ZERO;       // 总退款金额
        BigDecimal totalBonusAmount = BigDecimal.ZERO;        // 总赠送金额

        int rechargeCount = 0;   // 充值次数
        int consumeCount = 0;    // 消费次数
        int refundCount = 0;     // 退款次数
        int bonusCount = 0;      // 赠送次数

        // 按业务类型分组统计
        Map<String, BigDecimal> businessTypeAmounts = new HashMap<>();
        Map<String, Integer> businessTypeCounts = new HashMap<>();

        // 遍历流水记录进行统计
        for (MemberWalletDetail detail : detailList) {
            // 过滤日期范围
            if (isInDateRange(detail.getCreateTime(), startDate, endDate)) {
                String businessType = detail.getBusinessType();
                BigDecimal changeAmount = detail.getChangeAmount();

                // 按业务类型统计
                businessTypeAmounts.merge(businessType, changeAmount.abs(), BigDecimal::add);
                businessTypeCounts.merge(businessType, 1, Integer::sum);

                // 分类统计
                switch (businessType) {
                    case "RECHARGE":
                        totalRechargeAmount = totalRechargeAmount.add(changeAmount.abs());
                        rechargeCount++;
                        break;
                    case "CONSUME":
                        totalConsumeAmount = totalConsumeAmount.add(changeAmount.abs());
                        consumeCount++;
                        break;
                    case "REFUND":
                        totalRefundAmount = totalRefundAmount.add(changeAmount.abs());
                        refundCount++;
                        break;
                    case "ACTIVITY_BONUS":
                        totalBonusAmount = totalBonusAmount.add(changeAmount.abs());
                        bonusCount++;
                        break;
                }
            }
        }

        // 组装汇总数据
        summary.put("totalRechargeAmount", totalRechargeAmount);
        summary.put("totalConsumeAmount", totalConsumeAmount);
        summary.put("totalRefundAmount", totalRefundAmount);
        summary.put("totalBonusAmount", totalBonusAmount);

        summary.put("rechargeCount", rechargeCount);
        summary.put("consumeCount", consumeCount);
        summary.put("refundCount", refundCount);
        summary.put("bonusCount", bonusCount);

        summary.put("businessTypeAmounts", businessTypeAmounts);
        summary.put("businessTypeCounts", businessTypeCounts);

        // 计算统计期间
        summary.put("startDate", startDate);
        summary.put("endDate", endDate);
        summary.put("totalRecords", detailList.size());

        // 计算净流入金额（充值+退款+赠送-消费）
        BigDecimal netInflow = totalRechargeAmount.add(totalRefundAmount).add(totalBonusAmount).subtract(totalConsumeAmount);
        summary.put("netInflow", netInflow);

        return summary;
    }

    /**
     * 判断日期是否在指定范围内
     */
    private boolean isInDateRange(Date createTime, LocalDate startDate, LocalDate endDate) {
        if (createTime == null) {
            return false;
        }

        LocalDateTime createDateTime = createTime.toInstant()
                .atZone(java.time.ZoneId.systemDefault())
                .toLocalDateTime();
        LocalDate createDate = createDateTime.toLocalDate();

        boolean afterStart = startDate == null || !createDate.isBefore(startDate);
        boolean beforeEnd = endDate == null || !createDate.isAfter(endDate);

        return afterStart && beforeEnd;
    }

}
