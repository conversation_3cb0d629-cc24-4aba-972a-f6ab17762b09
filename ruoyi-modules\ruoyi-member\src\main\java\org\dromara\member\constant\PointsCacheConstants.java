package org.dromara.member.constant;

/**
 * 积分缓存常量
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public class PointsCacheConstants {

    /**
     * 积分规则缓存前缀
     */
    public static final String POINTS_RULE_CACHE_PREFIX = "points:rule:";

    /**
     * 有效积分规则缓存key
     */
    public static final String ACTIVE_POINTS_RULES_CACHE_KEY = "points:rules:active";

    /**
     * 会员积分余额缓存前缀
     */
    public static final String MEMBER_POINTS_BALANCE_CACHE_PREFIX = "points:balance:";

    /**
     * 会员积分账户缓存前缀
     */
    public static final String MEMBER_POINTS_ACCOUNT_CACHE_PREFIX = "points:account:";

    /**
     * 缓存过期时间（秒）
     */
    public static final long CACHE_EXPIRE_TIME = 3600; // 1小时

    /**
     * 积分规则缓存过期时间（秒）
     */
    public static final long RULES_CACHE_EXPIRE_TIME = 1800; // 30分钟

    /**
     * 积分余额缓存过期时间（秒）
     */
    public static final long BALANCE_CACHE_EXPIRE_TIME = 600; // 10分钟

    /**
     * 获取积分规则缓存key
     */
    public static String getPointsRuleCacheKey(Long ruleId) {
        return POINTS_RULE_CACHE_PREFIX + ruleId;
    }

    /**
     * 获取会员积分余额缓存key
     */
    public static String getMemberPointsBalanceCacheKey(Long memberId) {
        return MEMBER_POINTS_BALANCE_CACHE_PREFIX + memberId;
    }

    /**
     * 获取会员积分账户缓存key
     */
    public static String getMemberPointsAccountCacheKey(Long memberId) {
        return MEMBER_POINTS_ACCOUNT_CACHE_PREFIX + memberId;
    }
}
