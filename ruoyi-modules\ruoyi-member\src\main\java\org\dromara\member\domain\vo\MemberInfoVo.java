package org.dromara.member.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.member.domain.MemberInfo;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 会员信息视图对象 member_info
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MemberInfo.class)
public class MemberInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员ID
     */
    @ExcelProperty(value = "会员ID")
    private Long id;

    /**
     * 会员编号（唯一）
     */
    @ExcelProperty(value = "会员编号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "唯=一")
    private String memberNo;

    /**
     * 昵称
     */
    @ExcelProperty(value = "昵称")
    private String nickname;

    /**
     * 真实姓名
     */
    @ExcelProperty(value = "真实姓名")
    private String realName;

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号")
    private String phone;

    /**
     * 邮箱
     */
    @ExcelProperty(value = "邮箱")
    private String email;

    /**
     * 头像URL
     */
    @ExcelProperty(value = "头像URL")
    private String avatar;

    /**
     * 性别：0-未知，1-男，2-女
     */
    @ExcelProperty(value = "性别：0-未知，1-男，2-女")
    private String gender;

    /**
     * 生日
     */
    @ExcelProperty(value = "生日")
    private Date birthday;

    /**
     * 省份
     */
    @ExcelProperty(value = "省份")
    private String province;

    /**
     * 城市
     */
    @ExcelProperty(value = "城市")
    private String city;

    /**
     * 区县
     */
    @ExcelProperty(value = "区县")
    private String district;

    /**
     * 详细地址
     */
    @ExcelProperty(value = "详细地址")
    private String address;

    /**
     * 登录密码
     */
    @ExcelProperty(value = "登录密码")
    private String password;

    /**
     * 密码盐值
     */
    @ExcelProperty(value = "密码盐值")
    private String salt;

    /**
     * 注册渠道：APP, WEB, WECHAT, ALIPAY
     */
    @ExcelProperty(value = "注册渠道：APP, WEB, WECHAT, ALIPAY")
    private String registerChannel;

    /**
     * 注册IP
     */
    @ExcelProperty(value = "注册IP")
    private String registerIp;

    /**
     * 状态：0-正常，1-禁用，2-注销
     */
    @ExcelProperty(value = "状态：0-正常，1-禁用，2-注销")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
