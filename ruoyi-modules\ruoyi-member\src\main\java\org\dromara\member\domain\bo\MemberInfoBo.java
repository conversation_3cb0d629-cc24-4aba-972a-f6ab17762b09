package org.dromara.member.domain.bo;

import org.dromara.member.domain.MemberInfo;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 会员信息业务对象 member_info
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MemberInfo.class, reverseConvertGenerate = false)
public class MemberInfoBo extends BaseEntity {

    /**
     * 会员ID
     */
    private Long id;

    /**
     * 会员编号（唯一）
     */
    private String memberNo;

    /**
     * 昵称
     */
    @NotBlank(message = "昵称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String nickname;

    /**
     * 真实姓名
     */
    @NotBlank(message = "真实姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String realName;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phone;

    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空", groups = { AddGroup.class, EditGroup.class })
    private String email;

    /**
     * 头像URL
     */
    @NotBlank(message = "头像URL不能为空", groups = { AddGroup.class, EditGroup.class })
    private String avatar;

    /**
     * 性别：0-未知，1-男，2-女
     */
    @NotBlank(message = "性别：0-未知，1-男，2-女不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gender;

    /**
     * 生日
     */
    @NotNull(message = "生日不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date birthday;

    /**
     * 省份
     */
    @NotBlank(message = "省份不能为空", groups = { AddGroup.class, EditGroup.class })
    private String province;

    /**
     * 城市
     */
    @NotBlank(message = "城市不能为空", groups = { AddGroup.class, EditGroup.class })
    private String city;

    /**
     * 区县
     */
    @NotBlank(message = "区县不能为空", groups = { AddGroup.class, EditGroup.class })
    private String district;

    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String address;

    /**
     * 登录密码
     */
    @NotBlank(message = "登录密码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String password;

    /**
     * 密码盐值
     */
    @NotBlank(message = "密码盐值不能为空", groups = { AddGroup.class, EditGroup.class })
    private String salt;

    /**
     * 注册渠道：APP, WEB, WECHAT, ALIPAY
     */
    @NotBlank(message = "注册渠道：APP, WEB, WECHAT, ALIPAY不能为空", groups = { AddGroup.class, EditGroup.class })
    private String registerChannel;

    /**
     * 注册IP
     */
    @NotBlank(message = "注册IP不能为空", groups = { AddGroup.class, EditGroup.class })
    private String registerIp;

    /**
     * 状态：0-正常，1-禁用，2-注销
     */
    private String status;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
