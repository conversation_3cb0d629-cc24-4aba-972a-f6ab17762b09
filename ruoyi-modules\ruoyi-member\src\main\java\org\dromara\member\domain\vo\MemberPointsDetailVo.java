package org.dromara.member.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.member.domain.MemberPointsDetail;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 积分明细记录视图对象 member_points_detail
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MemberPointsDetail.class)
public class MemberPointsDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 会员ID
     */
    @ExcelProperty(value = "会员ID")
    private Long memberId;

    /**
     * 会员名称
     */
    @ExcelProperty(value = "会员名称")
    private String memberName;

    /**
     * 关联订单号
     */
    @ExcelProperty(value = "关联订单号")
    private String orderNo;

    /**
     * 业务类型：1-消费获得，2-签到获得，3-活动获得，4-积分过期，5-管理员调整
     */
    @ExcelProperty(value = "业务类型：1-消费获得，2-签到获得，3-活动获得，4-积分过期，5-管理员调整")
    private Integer businessType;

    /**
     * 积分变动数量（正数为增加，负数为减少）
     */
    @ExcelProperty(value = "积分变动数量", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "正=数为增加，负数为减少")
    private Long pointsChange;

    /**
     * 变动前积分余额
     */
    @ExcelProperty(value = "变动前积分余额")
    private Long pointsBefore;

    /**
     * 变动后积分余额
     */
    @ExcelProperty(value = "变动后积分余额")
    private Long pointsAfter;

    /**
     * 关联规则ID
     */
    @ExcelProperty(value = "关联规则ID")
    private Long ruleId;

    /**
     * 消费金额
     */
    @ExcelProperty(value = "消费金额")
    private Long consumeAmount;

    /**
     * 积分过期时间（可选）
     */
    @ExcelProperty(value = "积分过期时间", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "可=选")
    private Date expireTime;

    /**
     * 备注说明
     */
    @ExcelProperty(value = "备注说明")
    private String remark;


}
