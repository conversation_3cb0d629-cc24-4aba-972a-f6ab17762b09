# 储值系统Controller层API文档

## 概述

储值系统Controller层已完成基础结构搭建，包含6个主要Controller，涵盖会员管理、钱包操作、配置管理等核心功能。

## 1. 会员管理Controller (`MemberController`)

### 基础路径：`/member`

#### 1.1 会员注册
- **接口**：`POST /member/register`
- **功能**：创建新会员
- **参数**：MemberInfo对象
- **返回**：会员信息

#### 1.2 获取会员信息
- **接口**：`GET /member/{memberId}`
- **功能**：根据会员ID获取会员信息
- **参数**：memberId (路径参数)
- **返回**：会员详细信息

#### 1.3 根据会员编号获取信息
- **接口**：`GET /member/no/{memberNo}`
- **功能**：根据会员编号获取会员信息
- **参数**：memberNo (路径参数)
- **返回**：会员详细信息

#### 1.4 根据手机号获取信息
- **接口**：`GET /member/phone/{phone}`
- **功能**：根据手机号获取会员信息
- **参数**：phone (路径参数)
- **返回**：会员详细信息

#### 1.5 更新会员信息
- **接口**：`PUT /member`
- **功能**：更新会员信息
- **参数**：MemberInfo对象
- **返回**：操作结果

#### 1.6 获取钱包余额
- **接口**：`GET /member/{memberId}/wallet/balance`
- **功能**：获取会员钱包余额信息
- **参数**：memberId (路径参数)
- **返回**：钱包余额详情

#### 1.7 验证会员状态
- **接口**：`GET /member/{memberId}/validate`
- **功能**：验证会员状态是否可用
- **参数**：memberId (路径参数)
- **返回**：布尔值

#### 1.8 更新登录信息
- **接口**：`POST /member/{memberId}/login`
- **功能**：更新会员最后登录信息
- **参数**：memberId (路径参数), loginIp (请求参数)
- **返回**：操作结果

## 2. 钱包管理Controller (`WalletController`)

### 基础路径：`/wallet`

#### 2.1 获取钱包余额
- **接口**：`GET /wallet/{memberId}/balance`
- **功能**：获取会员钱包余额
- **参数**：memberId (路径参数)
- **返回**：钱包余额信息

#### 2.2 钱包充值
- **接口**：`POST /wallet/recharge`
- **功能**：执行钱包充值操作
- **参数**：RechargeRequestDto对象
- **返回**：充值结果信息

#### 2.3 钱包消费
- **接口**：`POST /wallet/consume`
- **功能**：执行钱包消费操作
- **参数**：ConsumeRequestDto对象
- **返回**：消费结果信息

#### 2.4 钱包退款
- **接口**：`POST /wallet/refund`
- **功能**：执行钱包退款操作
- **参数**：RefundRequestDto对象
- **返回**：退款结果信息

#### 2.5 冻结余额
- **接口**：`POST /wallet/{memberId}/freeze`
- **功能**：冻结指定金额的余额
- **参数**：memberId (路径参数), amount (请求参数), reason (请求参数)
- **返回**：操作结果

#### 2.6 解冻余额
- **接口**：`POST /wallet/{memberId}/unfreeze`
- **功能**：解冻指定金额的余额
- **参数**：memberId (路径参数), amount (请求参数), reason (请求参数)
- **返回**：操作结果

#### 2.7 检查余额
- **接口**：`GET /wallet/{memberId}/check-balance`
- **功能**：检查余额是否充足
- **参数**：memberId (路径参数), amount (请求参数)
- **返回**：布尔值

#### 2.8 创建钱包账户
- **接口**：`POST /wallet/{memberId}/create`
- **功能**：为会员创建钱包账户
- **参数**：memberId (路径参数)
- **返回**：操作结果

## 3. 充值活动配置Controller (`RechargeActivityController`)

### 基础路径：`/config/recharge-activity`

#### 3.1 获取活动列表
- **接口**：`GET /config/recharge-activity/list`
- **功能**：获取所有充值活动配置
- **返回**：活动配置列表

#### 3.2 获取有效活动
- **接口**：`GET /config/recharge-activity/active`
- **功能**：获取当前有效的充值活动
- **返回**：有效活动列表

#### 3.3 匹配活动
- **接口**：`GET /config/recharge-activity/match`
- **功能**：根据充值金额匹配活动
- **参数**：amount (请求参数)
- **返回**：匹配的活动配置

#### 3.4 获取活动详情
- **接口**：`GET /config/recharge-activity/{id}`
- **功能**：根据ID获取活动配置详情
- **参数**：id (路径参数)
- **返回**：活动配置详情

#### 3.5 新增活动
- **接口**：`POST /config/recharge-activity`
- **功能**：新增充值活动配置
- **参数**：RechargeActivityConfig对象
- **返回**：操作结果

#### 3.6 修改活动
- **接口**：`PUT /config/recharge-activity`
- **功能**：修改充值活动配置
- **参数**：RechargeActivityConfig对象
- **返回**：操作结果

#### 3.7 删除活动
- **接口**：`DELETE /config/recharge-activity/{ids}`
- **功能**：删除充值活动配置
- **参数**：ids (路径参数，数组)
- **返回**：操作结果

#### 3.8 启用/禁用活动
- **接口**：`POST /config/recharge-activity/{id}/enable`
- **接口**：`POST /config/recharge-activity/{id}/disable`
- **功能**：启用或禁用活动
- **参数**：id (路径参数)
- **返回**：操作结果

## 4. 消费策略配置Controller (`ConsumeStrategyController`)

### 基础路径：`/config/consume-strategy`

#### 4.1 获取策略列表
- **接口**：`GET /config/consume-strategy/list`
- **功能**：获取所有消费策略配置
- **返回**：策略配置列表

#### 4.2 获取默认策略
- **接口**：`GET /config/consume-strategy/default`
- **功能**：获取默认消费策略
- **返回**：默认策略配置

#### 4.3 获取策略详情
- **接口**：`GET /config/consume-strategy/{id}`
- **功能**：根据ID获取策略配置详情
- **参数**：id (路径参数)
- **返回**：策略配置详情

#### 4.4 新增策略
- **接口**：`POST /config/consume-strategy`
- **功能**：新增消费策略配置
- **参数**：ConsumeStrategyConfig对象
- **返回**：操作结果

#### 4.5 修改策略
- **接口**：`PUT /config/consume-strategy`
- **功能**：修改消费策略配置
- **参数**：ConsumeStrategyConfig对象
- **返回**：操作结果

#### 4.6 删除策略
- **接口**：`DELETE /config/consume-strategy/{ids}`
- **功能**：删除消费策略配置
- **参数**：ids (路径参数，数组)
- **返回**：操作结果

#### 4.7 设置默认策略
- **接口**：`POST /config/consume-strategy/{id}/set-default`
- **功能**：设置默认消费策略
- **参数**：id (路径参数)
- **返回**：操作结果

#### 4.8 启用/禁用策略
- **接口**：`POST /config/consume-strategy/{id}/enable`
- **接口**：`POST /config/consume-strategy/{id}/disable`
- **功能**：启用或禁用策略
- **参数**：id (路径参数)
- **返回**：操作结果

## 5. 退款规则配置Controller (`RefundRuleController`)

### 基础路径：`/config/refund-rule`

#### 5.1 获取规则列表
- **接口**：`GET /config/refund-rule/list`
- **功能**：获取所有退款规则配置
- **返回**：规则配置列表

#### 5.2 获取默认规则
- **接口**：`GET /config/refund-rule/default`
- **功能**：获取默认退款规则
- **返回**：默认规则配置

#### 5.3 匹配规则
- **接口**：`GET /config/refund-rule/match`
- **功能**：根据订单金额匹配退款规则
- **参数**：orderAmount (请求参数)
- **返回**：匹配的规则配置

#### 5.4 获取规则详情
- **接口**：`GET /config/refund-rule/{id}`
- **功能**：根据ID获取规则配置详情
- **参数**：id (路径参数)
- **返回**：规则配置详情

#### 5.5 新增规则
- **接口**：`POST /config/refund-rule`
- **功能**：新增退款规则配置
- **参数**：RefundRuleConfig对象
- **返回**：操作结果

#### 5.6 修改规则
- **接口**：`PUT /config/refund-rule`
- **功能**：修改退款规则配置
- **参数**：RefundRuleConfig对象
- **返回**：操作结果

#### 5.7 删除规则
- **接口**：`DELETE /config/refund-rule/{ids}`
- **功能**：删除退款规则配置
- **参数**：ids (路径参数，数组)
- **返回**：操作结果

#### 5.8 设置默认规则
- **接口**：`POST /config/refund-rule/{id}/set-default`
- **功能**：设置默认退款规则
- **参数**：id (路径参数)
- **返回**：操作结果

#### 5.9 启用/禁用规则
- **接口**：`POST /config/refund-rule/{id}/enable`
- **接口**：`POST /config/refund-rule/{id}/disable`
- **功能**：启用或禁用规则
- **参数**：id (路径参数)
- **返回**：操作结果

## 6. 钱包流水记录Controller (`WalletDetailController`)

### 基础路径：`/wallet/detail`

#### 6.1 分页查询流水
- **接口**：`GET /wallet/detail/page`
- **功能**：分页查询钱包流水记录
- **参数**：PageQuery, memberId, businessType, amountType, orderNo, startDate, endDate
- **返回**：分页数据

#### 6.2 根据会员查询流水
- **接口**：`GET /wallet/detail/member/{memberId}`
- **功能**：根据会员ID查询流水记录
- **参数**：memberId (路径参数), businessType, limit
- **返回**：流水记录列表

#### 6.3 根据订单查询流水
- **接口**：`GET /wallet/detail/order/{orderNo}`
- **功能**：根据订单号查询流水记录
- **参数**：orderNo (路径参数)
- **返回**：流水记录列表

#### 6.4 获取流水详情
- **接口**：`GET /wallet/detail/{id}`
- **功能**：根据ID获取流水记录详情
- **参数**：id (路径参数)
- **返回**：流水记录详情

#### 6.5 导出流水记录
- **接口**：`POST /wallet/detail/export`
- **功能**：导出钱包流水记录
- **参数**：筛选条件
- **返回**：Excel文件

#### 6.6 流水汇总统计
- **接口**：`GET /wallet/detail/member/{memberId}/summary`
- **功能**：统计会员钱包流水汇总信息
- **参数**：memberId (路径参数), startDate, endDate
- **返回**：汇总统计数据

## 关键特性

### 1. 统一的响应格式
所有接口都使用统一的响应格式 `R<T>`，包含：
- 状态码
- 消息
- 数据
- 错误信息

### 2. 完善的参数校验
- 使用 `@Validated` 注解进行参数校验
- 路径参数使用 `@NotNull` 校验
- 请求体使用 Bean Validation 校验
- 金额参数使用 `@DecimalMin` 校验

### 3. 操作日志记录
- 重要操作使用 `@Log` 注解记录日志
- 包含操作标题和业务类型
- 自动记录操作人员和时间

### 4. 权限控制预留
- 继承 `BaseController` 提供基础功能
- 预留权限控制注解位置
- 支持细粒度权限管理

## 使用示例

### 充值流程
```http
# 1. 检查会员状态
GET /member/1/validate

# 2. 获取充值活动
GET /config/recharge-activity/match?amount=100

# 3. 执行充值
POST /wallet/recharge
{
  "memberId": 1,
  "rechargeAmount": 100.00,
  "paymentMethod": "WECHAT",
  "clientIp": "***********"
}
```

### 消费流程
```http
# 1. 检查余额
GET /wallet/1/check-balance?amount=50

# 2. 获取消费策略
GET /config/consume-strategy/default

# 3. 执行消费
POST /wallet/consume
{
  "memberId": 1,
  "consumeAmount": 50.00,
  "orderNo": "ORD20240101001",
  "strategyId": 1
}
```

### 退款流程
```http
# 1. 查询原订单流水
GET /wallet/detail/order/ORD20240101001

# 2. 获取退款规则
GET /config/refund-rule/match?orderAmount=50

# 3. 执行退款
POST /wallet/refund
{
  "memberId": 1,
  "originalOrderNo": "ORD20240101001",
  "refundAmount": 30.00,
  "refundReason": "用户主动退款"
}
```

## 后续工作

1. **Service层实现**：完成各Controller依赖的Service接口实现
2. **Mapper层实现**：完成数据访问层代码
3. **单元测试**：编写各接口的单元测试
4. **接口文档**：生成Swagger/OpenAPI文档
5. **权限控制**：集成权限管理系统
6. **性能优化**：添加缓存和异步处理

## 总结

Controller层已完成基础架构搭建，提供了完整的储值系统API接口。接口设计考虑了：
- 业务完整性：覆盖充值、消费、退款、配置管理等核心功能
- 易用性：清晰的路径设计和参数命名
- 扩展性：预留了权限控制和日志记录能力
- 安全性：完善的参数校验和错误处理

这为后续的Service层实现和前端开发提供了稳定的API基础。 