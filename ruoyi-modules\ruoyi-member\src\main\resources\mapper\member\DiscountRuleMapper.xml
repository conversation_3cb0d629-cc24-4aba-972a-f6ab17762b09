<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.member.mapper.DiscountRuleMapper">

    <resultMap type="org.dromara.member.domain.vo.DiscountRuleVo" id="DiscountRuleResult">
        <result property="id"    column="id"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="minAmount"    column="min_amount"    />
        <result property="maxAmount"    column="max_amount"    />
        <result property="discountValue"    column="discount_value"    />
        <result property="maxDiscountAmount"    column="max_discount_amount"    />
        <result property="applicableProducts"    column="applicable_products"    />
        <result property="excludeProducts"    column="exclude_products"    />
        <result property="userLimitType"    column="user_limit_type"    />
        <result property="userLimitCount"    column="user_limit_count"    />
        <result property="totalLimitCount"    column="total_limit_count"    />
        <result property="usedCount"    column="used_count"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="status"    column="status"    />
        <result property="priority"    column="priority"    />
        <result property="canStack"    column="can_stack"    />
        <result property="remark"    column="remark"    />
        <result property="createDept"    column="create_dept"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

</mapper>
