package org.dromara.member.service;

import org.dromara.member.domain.RechargeActivityConfig;

import java.math.BigDecimal;
import java.util.List;

/**
 * 充值活动配置Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IRechargeActivityService {

    /**
     * 查询充值活动配置列表
     *
     * @return 充值活动配置列表
     */
    List<RechargeActivityConfig> selectRechargeActivityList();

    /**
     * 查询有效的充值活动配置
     *
     * @return 有效的充值活动配置列表
     */
    List<RechargeActivityConfig> selectActiveConfigs();

    /**
     * 根据充值金额匹配最优活动
     *
     * @param amount 充值金额
     * @return 匹配的活动配置
     */
    RechargeActivityConfig matchActivityByAmount(BigDecimal amount);

    /**
     * 查询充值活动配置
     *
     * @param id 充值活动配置主键
     * @return 充值活动配置
     */
    RechargeActivityConfig selectRechargeActivityById(Long id);

    /**
     * 新增充值活动配置
     *
     * @param config 充值活动配置
     * @return 结果
     */
    int insertRechargeActivity(RechargeActivityConfig config);

    /**
     * 修改充值活动配置
     *
     * @param config 充值活动配置
     * @return 结果
     */
    int updateRechargeActivity(RechargeActivityConfig config);

    /**
     * 批量删除充值活动配置
     *
     * @param ids 需要删除的充值活动配置主键集合
     * @return 结果
     */
    int deleteRechargeActivityByIds(Long[] ids);

    /**
     * 启用充值活动配置
     *
     * @param id 充值活动配置主键
     * @return 结果
     */
    int enableRechargeActivity(Long id);

    /**
     * 禁用充值活动配置
     *
     * @param id 充值活动配置主键
     * @return 结果
     */
    int disableRechargeActivity(Long id);
} 