<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.member.mapper.DiscountUsageRecordMapper">

    <resultMap type="org.dromara.member.domain.vo.DiscountUsageRecordVo" id="DiscountUsageRecordResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="originalAmount"    column="original_amount"    />
        <result property="discountAmount"    column="discount_amount"    />
        <result property="finalAmount"    column="final_amount"    />
        <result property="applicableProducts"    column="applicable_products"    />
        <result property="usageTime"    column="usage_time"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createDept"    column="create_dept"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

</mapper>
