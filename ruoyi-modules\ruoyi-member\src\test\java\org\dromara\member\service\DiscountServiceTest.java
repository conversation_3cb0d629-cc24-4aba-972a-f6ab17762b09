package org.dromara.member.service;

import org.dromara.member.domain.dto.DiscountRequest;
import org.dromara.member.domain.dto.DiscountResult;
import org.dromara.member.domain.dto.DiscountUseRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 优惠服务测试
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class DiscountServiceTest {

    @Autowired
    private IDiscountService discountService;

    @Test
    public void testCalculateDiscount() {
        // 测试满减优惠计算
        DiscountRequest request = new DiscountRequest();
        request.setMemberId(1L);
        request.setOriginalAmount(new BigDecimal("150.00"));
        request.setIsNewUser(false);

        DiscountResult result = discountService.calculateDiscount(request);
        
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals(new BigDecimal("150.00"), result.getOriginalAmount());
        assertTrue(result.getTotalDiscountAmount().compareTo(BigDecimal.ZERO) >= 0);
        assertEquals(result.getOriginalAmount().subtract(result.getTotalDiscountAmount()), 
                    result.getFinalAmount());
    }

    @Test
    public void testCalculateDiscountWithSpecificRules() {
        // 测试指定规则的优惠计算
        DiscountRequest request = new DiscountRequest();
        request.setMemberId(1L);
        request.setOriginalAmount(new BigDecimal("200.00"));
        request.setRuleIds(Arrays.asList(1L, 2L)); // 假设存在这些规则
        request.setIsNewUser(false);

        DiscountResult result = discountService.calculateDiscount(request);
        
        assertNotNull(result);
        // 结果可能成功也可能失败，取决于规则是否存在和有效
    }

    @Test
    public void testPreviewDiscount() {
        // 测试优惠预览
        DiscountRequest request = new DiscountRequest();
        request.setMemberId(1L);
        request.setOriginalAmount(new BigDecimal("100.00"));
        request.setIsNewUser(true);

        DiscountResult result = discountService.previewDiscount(request);
        
        assertNotNull(result);
        assertTrue(result.getSuccess());
    }

    @Test
    public void testUseDiscount() {
        // 测试使用优惠
        DiscountUseRequest request = new DiscountUseRequest();
        request.setMemberId(1L);
        request.setOrderNo("TEST_ORDER_" + System.currentTimeMillis());
        request.setOriginalAmount(new BigDecimal("150.00"));
        request.setRuleIds(Arrays.asList(1L)); // 假设存在这个规则
        request.setRemark("测试使用优惠");

        DiscountResult result = discountService.useDiscount(request);
        
        assertNotNull(result);
        // 结果可能成功也可能失败，取决于规则是否存在和有效
    }

    @Test
    public void testProcessRefund() {
        // 测试退款处理
        String orderNo = "TEST_REFUND_ORDER_" + System.currentTimeMillis();
        BigDecimal refundAmount = new BigDecimal("50.00");

        Boolean result = discountService.processRefund(orderNo, refundAmount);
        
        assertNotNull(result);
        assertTrue(result); // 即使没有优惠记录，也应该返回true
    }

    @Test
    public void testGetBestDiscountPlans() {
        // 测试获取最优优惠方案
        DiscountRequest request = new DiscountRequest();
        request.setMemberId(1L);
        request.setOriginalAmount(new BigDecimal("300.00"));
        request.setIsNewUser(false);

        var plans = discountService.getBestDiscountPlans(request);
        
        assertNotNull(plans);
        // 方案数量可能为0，取决于是否有适用的规则
    }

}
