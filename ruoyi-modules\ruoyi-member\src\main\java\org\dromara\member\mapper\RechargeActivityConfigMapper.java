package org.dromara.member.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.member.domain.RechargeActivityConfig;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.math.BigDecimal;
import java.util.List;

/**
 * 充值活动配置Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface RechargeActivityConfigMapper extends BaseMapperPlus<RechargeActivityConfig, RechargeActivityConfig> {

    /**
     * 查询有效的充值活动配置
     */
    List<RechargeActivityConfig> selectActiveConfigs();

    /**
     * 根据充值金额匹配最佳活动
     */
    RechargeActivityConfig selectBestMatchActivity(@Param("amount") BigDecimal amount);

} 