package org.dromara.member.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 积分服务测试类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@SpringBootTest
@SpringJUnitConfig
@Transactional
public class PointsServiceTest {

    @Autowired
    private IPointsService pointsService;

    @Autowired
    private IPointsRuleService pointsRuleService;

    /**
     * 测试积分计算
     */
    @Test
    public void testCalculatePoints() {
        // 测试满2元加3积分
        Long points1 = pointsRuleService.calculatePoints(new BigDecimal("2.00"));
        assertEquals(3L, points1);

        // 测试满5元加10积分
        Long points2 = pointsRuleService.calculatePoints(new BigDecimal("5.00"));
        assertEquals(10L, points2);

        // 测试满10元按1:1比例送积分
        Long points3 = pointsRuleService.calculatePoints(new BigDecimal("15.00"));
        assertEquals(15L, points3);

        // 测试不满足条件
        Long points4 = pointsRuleService.calculatePoints(new BigDecimal("1.00"));
        assertEquals(0L, points4);
    }

    /**
     * 测试消费获得积分
     */
    @Test
    public void testConsumeEarnPoints() {
        Long memberId = 1L;
        String orderNo = "ORDER_TEST_001";

        // 创建积分账户
        pointsService.createMemberPointsAccount(memberId);

        // 测试消费2元获得3积分
        Long earnedPoints = pointsService.consumeEarnPoints(memberId, new BigDecimal("2.00"), orderNo);
        assertEquals(3L, earnedPoints);

        // 验证积分余额
        Long balance = pointsService.getMemberPointsBalance(memberId);
        assertEquals(3L, balance);

        // 再次消费5元获得10积分
        Long earnedPoints2 = pointsService.consumeEarnPoints(memberId, new BigDecimal("5.00"), "ORDER_TEST_002");
        assertEquals(10L, earnedPoints2);

        // 验证积分余额
        Long balance2 = pointsService.getMemberPointsBalance(memberId);
        assertEquals(13L, balance2);
    }

    /**
     * 测试积分账户创建
     */
    @Test
    public void testCreateAccount() {
        Long memberId = 999L;

        // 创建积分账户
        Boolean created = pointsService.createMemberPointsAccount(memberId);
        assertTrue(created);

        // 验证账户信息
        var account = pointsService.getMemberPointsAccount(memberId);
        assertNotNull(account);
        assertEquals(memberId, account.getMemberId());
        assertEquals(0L, account.getTotalPoints());
        assertEquals(0L, account.getAvailablePoints());
    }

    /**
     * 测试管理员调整积分
     */
    @Test
    public void testAdjustPoints() {
        Long memberId = 2L;

        // 创建积分账户
        pointsService.createMemberPointsAccount(memberId);

        // 管理员增加100积分
        Boolean adjusted = pointsService.adjustMemberPoints(memberId, 100L, "管理员调整");
        assertTrue(adjusted);

        // 验证积分余额
        Long balance = pointsService.getMemberPointsBalance(memberId);
        assertEquals(100L, balance);
    }
}
