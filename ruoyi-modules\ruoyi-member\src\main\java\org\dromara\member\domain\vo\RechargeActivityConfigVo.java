package org.dromara.member.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.member.domain.RechargeActivityConfig;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 充值活动配置视图对象 recharge_activity_config
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = RechargeActivityConfig.class)
public class RechargeActivityConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 活动名称
     */
    @ExcelProperty(value = "活动名称")
    private String activityName;

    /**
     * 充值金额
     */
    @ExcelProperty(value = "充值金额")
    private Long rechargeAmount;

    /**
     * 赠送金额
     */
    @ExcelProperty(value = "赠送金额")
    private Long bonusAmount;

    /**
     * 活动开始时间
     */
    @ExcelProperty(value = "活动开始时间")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ExcelProperty(value = "活动结束时间")
    private Date endTime;

    /**
     * 状态：0-启用，1-禁用
     */
    @ExcelProperty(value = "状态：0-启用，1-禁用")
    private String status;

    /**
     * 排序（数字越大优先级越高）
     */
    @ExcelProperty(value = "排序", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "数=字越大优先级越高")
    private Long sortOrder;

    /**
     * 备注说明
     */
    @ExcelProperty(value = "备注说明")
    private String remark;


}
