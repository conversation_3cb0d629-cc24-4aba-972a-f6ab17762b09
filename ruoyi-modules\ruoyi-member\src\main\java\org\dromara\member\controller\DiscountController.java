package org.dromara.member.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.member.domain.dto.DiscountRequest;
import org.dromara.member.domain.dto.DiscountResult;
import org.dromara.member.domain.dto.DiscountUseRequest;
import org.dromara.member.domain.vo.DiscountUsageRecordVo;
import org.dromara.member.service.IDiscountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 优惠管理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Validated
@RestController
@RequestMapping("/member/discount")
public class DiscountController extends BaseController {

    @Autowired
    private IDiscountService discountService;

    /**
     * 计算订单优惠
     */
    @SaCheckPermission("member:discount:calculate")
    @PostMapping("/calculate")
    public R<DiscountResult> calculateDiscount(@Validated @RequestBody DiscountRequest request) {
        DiscountResult result = discountService.calculateDiscount(request);
        return result.getSuccess() ? R.ok(result) : R.fail(result.getErrorMessage());
    }

    /**
     * 预览优惠效果
     */
    @SaCheckPermission("member:discount:preview")
    @PostMapping("/preview")
    public R<DiscountResult> previewDiscount(@Validated @RequestBody DiscountRequest request) {
        DiscountResult result = discountService.previewDiscount(request);
        return result.getSuccess() ? R.ok(result) : R.fail(result.getErrorMessage());
    }

    /**
     * 获取优惠方案
     */
    @SaCheckPermission("member:discount:query")
    @PostMapping("/plans")
    public R<List<DiscountResult.DiscountPlan>> getDiscountPlans(@Validated @RequestBody DiscountRequest request) {
        List<DiscountResult.DiscountPlan> plans = discountService.getBestDiscountPlans(request);
        return R.ok(plans);
    }

    /**
     * 使用优惠
     */
    @SaCheckPermission("member:discount:use")
    @Log(title = "使用优惠", businessType = BusinessType.UPDATE)
    @RepeatSubmit(interval = 2, timeUnit = TimeUnit.SECONDS, message = "请勿重复提交")
    @PostMapping("/use")
    public R<DiscountResult> useDiscount(@Validated @RequestBody DiscountUseRequest request) {
        DiscountResult result = discountService.useDiscount(request);
        return result.getSuccess() ? R.ok(result) : R.fail(result.getErrorMessage());
    }

    /**
     * 优惠退款
     */
    @SaCheckPermission("member:discount:refund")
    @Log(title = "优惠退款", businessType = BusinessType.UPDATE)
    @RepeatSubmit(interval = 2, timeUnit = TimeUnit.SECONDS, message = "请勿重复提交")
    @PostMapping("/refund")
    public R<Void> processRefund(@RequestParam @NotBlank(message = "订单号不能为空") String orderNo,
                                 @RequestParam @NotNull(message = "退款金额不能为空") BigDecimal refundAmount) {
        boolean success = discountService.processRefund(orderNo, refundAmount);
        return success ? R.ok() : R.fail("退款处理失败");
    }

    /**
     * 获取会员优惠使用历史
     */
    @SaCheckPermission("member:discount:query")
    @GetMapping("/usage/history/{memberId}")
    public TableDataInfo<DiscountUsageRecordVo> getMemberUsageHistory(
            @PathVariable @NotNull(message = "会员ID不能为空") Long memberId,
            PageQuery pageQuery) {
        return discountService.getMemberUsageHistory(memberId, pageQuery);
    }

}
