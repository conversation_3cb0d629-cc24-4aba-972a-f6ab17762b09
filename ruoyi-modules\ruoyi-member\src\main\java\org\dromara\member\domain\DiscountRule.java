package org.dromara.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 优惠规则配置对象 discount_rule
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("discount_rule")
public class DiscountRule extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则类型：1-满减，2-折扣，3-买赠，4-阶梯满减
     */
    private String ruleType;

    /**
     * 最小消费金额
     */
    private BigDecimal minAmount;

    /**
     * 最大消费金额（null表示无上限）
     */
    private BigDecimal maxAmount;

    /**
     * 优惠值（满减金额或折扣比例）
     */
    private BigDecimal discountValue;

    /**
     * 最大优惠金额（折扣时使用）
     */
    private BigDecimal maxDiscountAmount;

    /**
     * 适用商品（JSON格式，null表示全商品）
     */
    private String applicableProducts;

    /**
     * 排除商品（JSON格式）
     */
    private String excludeProducts;

    /**
     * 用户限制类型：0-无限制，1-每人限用，2-新用户专享
     */
    private String userLimitType;

    /**
     * 每人限用次数
     */
    private Integer userLimitCount;

    /**
     * 总使用次数限制
     */
    private Integer totalLimitCount;

    /**
     * 已使用次数
     */
    private Integer usedCount;

    /**
     * 规则生效开始时间
     */
    private Date startTime;

    /**
     * 规则生效结束时间
     */
    private Date endTime;

    /**
     * 状态：0-启用，1-禁用
     */
    private String status;

    /**
     * 优先级（数字越大优先级越高）
     */
    private Integer priority;

    /**
     * 是否可叠加：0-不可叠加，1-可叠加
     */
    private String canStack;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

}
