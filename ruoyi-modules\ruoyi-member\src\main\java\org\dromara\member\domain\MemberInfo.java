package org.dromara.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.member.domain.base.SimpleBaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 会员信息对象 member_info
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("member_info")
public class MemberInfo extends SimpleBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 会员编号（唯一）
     */
    private String memberNo;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 性别：0-未知，1-男，2-女
     */
    private String gender;

    /**
     * 生日
     */
    private Date birthday;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区县
     */
    private String district;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 登录密码
     */
    private String password;

    /**
     * 密码盐值
     */
    private String salt;

    /**
     * 注册渠道：APP, WEB, WECHAT, ALIPAY
     */
    private String registerChannel;

    /**
     * 注册IP
     */
    private String registerIp;


    /**
     * 状态：0-正常，1-禁用，2-注销
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

}
