package org.dromara.member.service;

import org.dromara.member.domain.vo.PointsRuleVo;
import org.dromara.member.domain.bo.PointsRuleBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 积分规则配置Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IPointsRuleService {

    /**
     * 查询积分规则配置
     */
    PointsRuleVo queryById(Long id);

    /**
     * 查询积分规则配置列表
     */
    TableDataInfo<PointsRuleVo> queryPageList(PointsRuleBo bo, PageQuery pageQuery);

    /**
     * 查询积分规则配置列表
     */
    List<PointsRuleVo> queryList(PointsRuleBo bo);

    /**
     * 新增积分规则配置
     */
    Boolean insertByBo(PointsRuleBo bo);

    /**
     * 修改积分规则配置
     */
    Boolean updateByBo(PointsRuleBo bo);

    /**
     * 校验并批量删除积分规则配置信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取有效的积分规则列表
     */
    List<PointsRuleVo> getActiveRules();

    /**
     * 根据消费金额计算积分
     */
    Long calculatePoints(BigDecimal amount);

    /**
     * 根据消费金额找到最匹配的规则
     */
    PointsRuleVo findBestRule(BigDecimal amount);
}
