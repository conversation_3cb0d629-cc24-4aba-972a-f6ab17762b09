package org.dromara.member.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.member.domain.MemberInfo;
import org.dromara.member.domain.bo.MemberInfoBo;
import org.dromara.member.domain.vo.MemberInfoVo;
import org.dromara.member.domain.vo.WalletBalanceVo;
import org.dromara.member.service.IMemberService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 会员管理Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/member/info")
public class MemberController extends BaseController {

    private final IMemberService memberService;

    /**
     * 查询会员信息列表
     */
    @SaCheckPermission("member:info:list")
    @GetMapping("/list")
    public TableDataInfo<MemberInfoVo> list(MemberInfoBo bo, PageQuery pageQuery) {
        return memberService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出会员信息列表
     */
    @SaCheckPermission("member:info:export")
    @Log(title = "会员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MemberInfoBo bo, HttpServletResponse response) {
        List<MemberInfoVo> list = memberService.queryList(bo);
        ExcelUtil.exportExcel(list, "会员信息", MemberInfoVo.class, response);
    }



    /**
     * 会员注册
     */
    @Log(title = "会员注册", businessType = BusinessType.INSERT)
    @PostMapping("/register")
    public R register(@Validated @RequestBody MemberInfoBo memberInfo) {
        boolean result = memberService.createMember(memberInfo);
        if (result) {
            return R.ok();
        }
        return R.fail("注册失败");
    }

    /**
     * 根据会员ID获取会员信息
     */
    /**
     * 获取会员信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("member:info:query")
    @GetMapping("/{id}")
    public R<MemberInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long id) {
        return R.ok(memberService.getMemberById(id));
    }

    /**
     * 删除会员信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("member:info:remove")
    @Log(title = "会员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(memberService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 根据会员编号获取会员信息
     */
    @GetMapping("/no/{memberNo}")
    public R<MemberInfo> getMemberByNo(@PathVariable String memberNo) {
        MemberInfo memberInfo = memberService.getMemberByNo(memberNo);
        return R.ok(memberInfo);
    }

    /**
     * 根据手机号获取会员信息
     */
    @GetMapping("/phone/{phone}")
    public R<MemberInfo> getMemberByPhone(@PathVariable String phone) {
        MemberInfo memberInfo = memberService.getMemberByPhone(phone);
        return R.ok(memberInfo);
    }

    /**
     * 更新会员信息
     */
    @Log(title = "更新会员信息", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Void> updateMember(@Validated @RequestBody MemberInfo memberInfo) {
        boolean result = memberService.updateMember(memberInfo);
        return toAjax(result);
    }

    /**
     * 获取会员钱包余额
     */
    @GetMapping("/{memberId}/wallet/balance")
    public R<WalletBalanceVo> getWalletBalance(@NotNull(message = "会员ID不能为空") @PathVariable Long memberId) {
        WalletBalanceVo balance = memberService.getMemberWalletBalance(memberId);
        return R.ok(balance);
    }

    /**
     * 验证会员状态
     */
    @GetMapping("/{memberId}/validate")
    public R<Boolean> validateMemberStatus(@NotNull(message = "会员ID不能为空") @PathVariable Long memberId) {
        Boolean isValid = memberService.validateMemberStatus(memberId);
        return R.ok(isValid);
    }


}
