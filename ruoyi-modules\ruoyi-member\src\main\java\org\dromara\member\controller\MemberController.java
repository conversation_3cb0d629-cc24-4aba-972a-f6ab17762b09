package org.dromara.member.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.member.domain.MemberInfo;
import org.dromara.member.domain.vo.WalletBalanceVo;
import org.dromara.member.service.IMemberService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotNull;

/**
 * 会员管理Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/member")
public class MemberController extends BaseController {

    private final IMemberService memberService;

    /**
     * 会员注册
     */
    @Log(title = "会员注册", businessType = BusinessType.INSERT)
    @PostMapping("/register")
    public R<MemberInfo> register(@Validated @RequestBody MemberInfo memberInfo) {
        boolean result = memberService.createMember(memberInfo);
        if (result) {
            return R.ok(memberInfo);
        }
        return R.fail("注册失败");
    }

    /**
     * 根据会员ID获取会员信息
     */
    @GetMapping("/{memberId}")
    public R<MemberInfo> getMember(@NotNull(message = "会员ID不能为空") @PathVariable Long memberId) {
        MemberInfo memberInfo = memberService.getMemberById(memberId);
        return R.ok(memberInfo);
    }

    /**
     * 根据会员编号获取会员信息
     */
    @GetMapping("/no/{memberNo}")
    public R<MemberInfo> getMemberByNo(@PathVariable String memberNo) {
        MemberInfo memberInfo = memberService.getMemberByNo(memberNo);
        return R.ok(memberInfo);
    }

    /**
     * 根据手机号获取会员信息
     */
    @GetMapping("/phone/{phone}")
    public R<MemberInfo> getMemberByPhone(@PathVariable String phone) {
        MemberInfo memberInfo = memberService.getMemberByPhone(phone);
        return R.ok(memberInfo);
    }

    /**
     * 更新会员信息
     */
    @Log(title = "更新会员信息", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Void> updateMember(@Validated @RequestBody MemberInfo memberInfo) {
        boolean result = memberService.updateMember(memberInfo);
        return toAjax(result);
    }

    /**
     * 获取会员钱包余额
     */
    @GetMapping("/{memberId}/wallet/balance")
    public R<WalletBalanceVo> getWalletBalance(@NotNull(message = "会员ID不能为空") @PathVariable Long memberId) {
        WalletBalanceVo balance = memberService.getMemberWalletBalance(memberId);
        return R.ok(balance);
    }

    /**
     * 验证会员状态
     */
    @GetMapping("/{memberId}/validate")
    public R<Boolean> validateMemberStatus(@NotNull(message = "会员ID不能为空") @PathVariable Long memberId) {
        Boolean isValid = memberService.validateMemberStatus(memberId);
        return R.ok(isValid);
    }

    /**
     * 更新会员最后登录信息
     */
    @Log(title = "更新登录信息", businessType = BusinessType.UPDATE)
    @PostMapping("/{memberId}/login")
    public R<Void> updateLastLoginInfo(@NotNull(message = "会员ID不能为空") @PathVariable Long memberId,
                                       @RequestParam(required = false) String loginIp) {
        memberService.updateLastLoginInfo(memberId, loginIp);
        return R.ok();
    }

} 