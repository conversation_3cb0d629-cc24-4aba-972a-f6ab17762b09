package org.dromara.member.domain.bo;

import org.dromara.member.domain.DiscountUsageRecord;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 优惠使用记录业务对象 discount_usage_record
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = DiscountUsageRecord.class, reverseConvertGenerate = false)
public class DiscountUsageRecordBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 使用的规则ID
     */
    private Long ruleId;

    /**
     * 规则名称（冗余字段）
     */
    private String ruleName;

    /**
     * 规则类型
     */
    private String ruleType;

    /**
     * 原始金额
     */
    private BigDecimal originalAmount;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 最终金额
     */
    private BigDecimal finalAmount;

    /**
     * 适用商品信息（JSON格式）
     */
    @NotBlank(message = "适用商品信息（JSON格式）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applicableProducts;

    /**
     * 使用时间
     */
    @NotNull(message = "使用时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date usageTime;

    /**
     * 状态：1-已使用，2-已退款
     */
    @NotBlank(message = "状态：1-已使用，2-已退款不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 备注说明
     */
    @NotBlank(message = "备注说明不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
