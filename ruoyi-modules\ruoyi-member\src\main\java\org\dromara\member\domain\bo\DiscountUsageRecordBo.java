package org.dromara.member.domain.bo;

import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.member.domain.DiscountUsageRecord;
import org.dromara.common.core.annotation.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 优惠使用记录业务对象 discount_usage_record
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = DiscountUsageRecord.class, reverseConvertGenerate = false)
public class DiscountUsageRecordBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 会员ID
     */
    @NotNull(message = "会员ID不能为空")
    private Long memberId;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    @Size(max = 64, message = "订单号长度不能超过{max}个字符")
    private String orderNo;

    /**
     * 使用的规则ID
     */
    @NotNull(message = "规则ID不能为空")
    private Long ruleId;

    /**
     * 规则名称（冗余字段）
     */
    @NotBlank(message = "规则名称不能为空")
    @Size(max = 100, message = "规则名称长度不能超过{max}个字符")
    private String ruleName;

    /**
     * 规则类型
     */
    @NotBlank(message = "规则类型不能为空")
    private String ruleType;

    /**
     * 原始金额
     */
    @NotNull(message = "原始金额不能为空")
    @DecimalMin(value = "0.01", message = "原始金额必须大于0")
    private BigDecimal originalAmount;

    /**
     * 优惠金额
     */
    @NotNull(message = "优惠金额不能为空")
    @DecimalMin(value = "0.00", message = "优惠金额不能小于0")
    private BigDecimal discountAmount;

    /**
     * 最终金额
     */
    @NotNull(message = "最终金额不能为空")
    @DecimalMin(value = "0.00", message = "最终金额不能小于0")
    private BigDecimal finalAmount;

    /**
     * 适用商品信息（JSON格式）
     */
    private String applicableProducts;

    /**
     * 使用时间
     */
    private Date usageTime;

    /**
     * 状态：1-已使用，2-已退款
     */
    private String status;

    /**
     * 备注说明
     */
    @Size(max = 500, message = "备注说明长度不能超过{max}个字符")
    private String remark;

}
