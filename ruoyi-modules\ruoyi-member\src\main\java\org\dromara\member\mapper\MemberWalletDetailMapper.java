package org.dromara.member.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.member.domain.MemberWalletDetail;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.member.domain.vo.MemberWalletDetailVo;

import java.util.List;

/**
 * 钱包流水记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface MemberWalletDetailMapper extends BaseMapperPlus<MemberWalletDetail, MemberWalletDetailVo> {

    /**
     * 根据订单号查询流水记录
     */
    List<MemberWalletDetail> selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据会员ID查询最近的流水记录
     */
    List<MemberWalletDetail> selectRecentByMemberId(@Param("memberId") Long memberId,
                                                   @Param("limit") Integer limit);

    /**
     * 根据会员ID和业务类型查询流水记录
     */
    List<MemberWalletDetail> selectByMemberIdAndBusinessType(@Param("memberId") Long memberId,
                                                            @Param("businessType") String businessType);

}
