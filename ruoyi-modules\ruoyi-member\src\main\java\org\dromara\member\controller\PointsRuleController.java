package org.dromara.member.controller;

import java.util.List;
import java.util.concurrent.TimeUnit;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.member.domain.vo.PointsRuleVo;
import org.dromara.member.domain.bo.PointsRuleBo;
import org.dromara.member.service.IPointsRuleService;
import org.dromara.member.service.IPointsCacheService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 积分规则配置
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/member/pointsRule")
public class PointsRuleController extends BaseController {

    private final IPointsRuleService pointsRuleService;
    private final IPointsCacheService pointsCacheService;

    /**
     * 查询积分规则配置列表
     */
    @SaCheckPermission("member:pointsRule:list")
    @GetMapping("/list")
    public TableDataInfo<PointsRuleVo> list(PointsRuleBo bo, PageQuery pageQuery) {
        return pointsRuleService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出积分规则配置列表
     */
    @SaCheckPermission("member:pointsRule:export")
    @Log(title = "积分规则配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PointsRuleBo bo, HttpServletResponse response) {
        List<PointsRuleVo> list = pointsRuleService.queryList(bo);
        ExcelUtil.exportExcel(list, "积分规则配置", PointsRuleVo.class, response);
    }

    /**
     * 获取积分规则配置详细信息
     */
    @SaCheckPermission("member:pointsRule:query")
    @GetMapping("/{id}")
    public R<PointsRuleVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(pointsRuleService.queryById(id));
    }

    /**
     * 新增积分规则配置
     */
    @SaCheckPermission("member:pointsRule:add")
    @Log(title = "积分规则配置", businessType = BusinessType.INSERT)
    @RepeatSubmit(interval = 2, timeUnit = TimeUnit.SECONDS, message = "请勿重复提交")
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PointsRuleBo bo) {
        return toAjax(pointsRuleService.insertByBo(bo));
    }

    /**
     * 修改积分规则配置
     */
    @SaCheckPermission("member:pointsRule:edit")
    @Log(title = "积分规则配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PointsRuleBo bo) {
        return toAjax(pointsRuleService.updateByBo(bo));
    }

    /**
     * 删除积分规则配置
     */
    @SaCheckPermission("member:pointsRule:remove")
    @Log(title = "积分规则配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(pointsRuleService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 获取有效的积分规则列表
     */
    @SaCheckPermission("member:pointsRule:list")
    @GetMapping("/active")
    public R<List<PointsRuleVo>> getActiveRules() {
        return R.ok(pointsRuleService.getActiveRules());
    }

    /**
     * 刷新积分规则缓存
     */
    @SaCheckPermission("member:pointsRule:edit")
    @Log(title = "刷新积分规则缓存", businessType = BusinessType.UPDATE)
    @PostMapping("/cache/refresh")
    public R<Void> refreshCache() {
        pointsCacheService.clearRulesCache();
        return R.ok("缓存刷新成功");
    }
}
