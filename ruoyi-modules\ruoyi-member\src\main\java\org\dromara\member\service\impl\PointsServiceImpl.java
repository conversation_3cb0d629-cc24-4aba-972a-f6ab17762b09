package org.dromara.member.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.member.domain.bo.MemberPointsDetailBo;
import org.dromara.member.domain.vo.MemberPointsAccountVo;
import org.dromara.member.domain.vo.MemberPointsDetailVo;
import org.dromara.member.service.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * 积分服务门面实现类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PointsServiceImpl implements IPointsService {

    private final IPointsRuleService pointsRuleService;
    private final IMemberPointsAccountService memberPointsAccountService;
    private final IMemberPointsDetailService memberPointsDetailService;

    /**
     * 消费获得积分
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long consumeEarnPoints(Long memberId, BigDecimal amount, String orderNo) {
        try {
            // 计算可获得的积分
            Long points = calculatePointsByAmount(amount);
            if (points <= 0) {
                log.info("消费金额{}未达到积分获取条件", amount);
                return 0L;
            }

            // 为会员增加积分
            boolean success = memberPointsAccountService.addPoints(memberId, points, orderNo, amount);
            if (success) {
                log.info("会员{}消费{}元，获得{}积分", memberId, amount, points);
                return points;
            } else {
                log.error("会员{}积分增加失败", memberId);
                return 0L;
            }
        } catch (Exception e) {
            log.error("消费获得积分异常，会员ID：{}，消费金额：{}", memberId, amount, e);
            throw new RuntimeException("积分获取失败：" + e.getMessage());
        }
    }

    /**
     * 根据消费金额计算可获得的积分
     */
    @Override
    public Long calculatePointsByAmount(BigDecimal amount) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return 0L;
        }
        return pointsRuleService.calculatePoints(amount);
    }

    /**
     * 获取会员积分余额
     */
    @Override
    public Long getMemberPointsBalance(Long memberId) {
        return memberPointsAccountService.getMemberPoints(memberId);
    }

    /**
     * 获取会员积分账户信息
     */
    @Override
    public MemberPointsAccountVo getMemberPointsAccount(Long memberId) {
        MemberPointsAccountVo account = memberPointsAccountService.queryByMemberId(memberId);
        if (account == null) {
            // 如果账户不存在，创建一个新账户
            createMemberPointsAccount(memberId);
            account = memberPointsAccountService.queryByMemberId(memberId);
        }
        return account;
    }

    /**
     * 获取会员积分历史记录
     */
    @Override
    public TableDataInfo<MemberPointsDetailVo> getMemberPointsHistory(Long memberId, PageQuery pageQuery) {
        return memberPointsDetailService.getMemberPointsHistory(memberId, pageQuery);
    }

    /**
     * 创建会员积分账户
     */
    @Override
    public Boolean createMemberPointsAccount(Long memberId) {
        return memberPointsAccountService.createAccount(memberId);
    }

    /**
     * 管理员调整积分
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean adjustMemberPoints(Long memberId, Long pointsChange, String remark) {
        try {
            if (pointsChange == 0) {
                log.warn("积分变动数量不能为0");
                return false;
            }

            if (pointsChange > 0) {
                // 增加积分
                return memberPointsAccountService.addPoints(memberId, pointsChange, null, null);
            } else {
                // 减少积分的逻辑需要额外实现
                // 这里暂时不支持减少积分
                log.warn("暂不支持减少积分操作");
                return false;
            }
        } catch (Exception e) {
            log.error("管理员调整积分异常，会员ID：{}，积分变动：{}", memberId, pointsChange, e);
            throw new RuntimeException("积分调整失败：" + e.getMessage());
        }
    }
}
