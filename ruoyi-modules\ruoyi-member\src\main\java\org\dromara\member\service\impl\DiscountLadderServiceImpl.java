package org.dromara.member.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.member.domain.DiscountLadder;
import org.dromara.member.domain.vo.DiscountLadderVo;
import org.dromara.member.mapper.DiscountLadderMapper;
import org.dromara.member.service.IDiscountLadderService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 阶梯优惠配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@RequiredArgsConstructor
@Service
public class DiscountLadderServiceImpl implements IDiscountLadderService {

    private final DiscountLadderMapper baseMapper;

    /**
     * 根据规则ID查询阶梯配置
     */
    @Override
    public List<DiscountLadderVo> getByRuleId(Long ruleId) {
        LambdaQueryWrapper<DiscountLadder> lqw = Wrappers.lambdaQuery();
        lqw.eq(DiscountLadder::getRuleId, ruleId);
        lqw.orderByAsc(DiscountLadder::getSortOrder);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 保存阶梯配置
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveLadders(Long ruleId, List<DiscountLadderVo> ladders) {
        // 先删除原有配置
        deleteByRuleId(ruleId);
        
        if (CollUtil.isEmpty(ladders)) {
            return true;
        }
        
        // 插入新配置
        for (int i = 0; i < ladders.size(); i++) {
            DiscountLadderVo ladderVo = ladders.get(i);
            DiscountLadder ladder = MapstructUtils.convert(ladderVo, DiscountLadder.class);
            ladder.setRuleId(ruleId);
            ladder.setSortOrder(i + 1);
            ladder.setCreateTime(new Date());
            baseMapper.insert(ladder);
        }
        
        return true;
    }

    /**
     * 删除规则的阶梯配置
     */
    @Override
    public Boolean deleteByRuleId(Long ruleId) {
        LambdaQueryWrapper<DiscountLadder> lqw = Wrappers.lambdaQuery();
        lqw.eq(DiscountLadder::getRuleId, ruleId);
        return baseMapper.delete(lqw) >= 0;
    }

}
