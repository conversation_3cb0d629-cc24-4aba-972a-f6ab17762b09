package org.dromara.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.member.domain.base.SimpleBaseEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 消费策略配置对象 consume_strategy_config
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("consume_strategy_config")
public class ConsumeStrategyConfig extends SimpleBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 策略类型：1-优先扣除充值金额，2-优先扣除赠送金额，3-按比例扣除
     */
    private String strategyType;

    /**
     * 充值金额扣除比例（0-100）
     */
    private BigDecimal rechargeRatio;

    /**
     * 赠送金额扣除比例（0-100）
     */
    private BigDecimal bonusRatio;

    /**
     * 状态：0-启用，1-禁用
     */
    private String status;

    /**
     * 是否默认策略：0-否，1-是
     */
    private String isDefault;

    /**
     * 备注说明
     */
    private String remark;

} 