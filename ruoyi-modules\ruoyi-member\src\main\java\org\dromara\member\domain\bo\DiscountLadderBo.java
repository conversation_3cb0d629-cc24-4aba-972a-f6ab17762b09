package org.dromara.member.domain.bo;

import org.dromara.member.domain.DiscountLadder;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 阶梯优惠配置业务对象 discount_ladder
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = DiscountLadder.class, reverseConvertGenerate = false)
public class DiscountLadderBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 关联规则ID
     */
    private Long ruleId;

    /**
     * 最小金额
     */
    private Long minAmount;

    /**
     * 最大金额
     */
    @NotNull(message = "最大金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long maxAmount;

    /**
     * 优惠值
     */
    private Long discountValue;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sortOrder;


}
