package org.dromara.member.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.member.domain.vo.DiscountRuleVo;
import org.dromara.member.service.IDiscountCacheService;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * 优惠缓存服务实现
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DiscountCacheServiceImpl implements IDiscountCacheService {

    private static final String ACTIVE_RULES_KEY = "discount:rules:active";
    private static final String MEMBER_STATS_KEY = "discount:member:stats:";
    private static final String RULE_DETAIL_KEY = "discount:rule:detail:";
    
    // 缓存过期时间
    private static final Duration CACHE_EXPIRE_TIME = Duration.ofHours(1);
    private static final Duration STATS_CACHE_EXPIRE_TIME = Duration.ofMinutes(30);

    /**
     * 缓存有效的优惠规则
     */
    @Override
    public void cacheActiveRules(List<DiscountRuleVo> rules) {
        if (CollUtil.isNotEmpty(rules)) {
            RedisUtils.setCacheObject(ACTIVE_RULES_KEY, rules, CACHE_EXPIRE_TIME);
            log.debug("缓存有效优惠规则，数量：{}", rules.size());
        }
    }

    /**
     * 获取缓存的有效优惠规则
     */
    @Override
    public List<DiscountRuleVo> getCachedActiveRules() {
        List<DiscountRuleVo> rules = RedisUtils.getCacheObject(ACTIVE_RULES_KEY);
        if (CollUtil.isNotEmpty(rules)) {
            log.debug("从缓存获取有效优惠规则，数量：{}", rules.size());
        }
        return rules;
    }

    /**
     * 清除规则缓存
     */
    @Override
    public void clearRulesCache() {
        RedisUtils.deleteObject(ACTIVE_RULES_KEY);
        log.debug("清除优惠规则缓存");
    }

    /**
     * 缓存会员使用统计
     */
    @Override
    public void cacheMemberUsageStats(Long memberId, Map<Long, Integer> stats) {
        if (ObjectUtil.isNotNull(memberId) && CollUtil.isNotEmpty(stats)) {
            String key = MEMBER_STATS_KEY + memberId;
            RedisUtils.setCacheObject(key, stats, STATS_CACHE_EXPIRE_TIME);
            log.debug("缓存会员{}使用统计，规则数量：{}", memberId, stats.size());
        }
    }

    /**
     * 获取缓存的会员使用统计
     */
    @Override
    public Map<Long, Integer> getCachedMemberUsageStats(Long memberId) {
        if (ObjectUtil.isNull(memberId)) {
            return null;
        }
        String key = MEMBER_STATS_KEY + memberId;
        Map<Long, Integer> stats = RedisUtils.getCacheObject(key);
        if (CollUtil.isNotEmpty(stats)) {
            log.debug("从缓存获取会员{}使用统计，规则数量：{}", memberId, stats.size());
        }
        return stats;
    }

    /**
     * 清除会员使用统计缓存
     */
    @Override
    public void clearMemberUsageStatsCache(Long memberId) {
        if (ObjectUtil.isNotNull(memberId)) {
            String key = MEMBER_STATS_KEY + memberId;
            RedisUtils.deleteObject(key);
            log.debug("清除会员{}使用统计缓存", memberId);
        }
    }

    /**
     * 缓存规则详情
     */
    @Override
    public void cacheRuleDetail(Long ruleId, DiscountRuleVo rule) {
        if (ObjectUtil.isNotNull(ruleId) && ObjectUtil.isNotNull(rule)) {
            String key = RULE_DETAIL_KEY + ruleId;
            RedisUtils.setCacheObject(key, rule, CACHE_EXPIRE_TIME);
            log.debug("缓存规则{}详情", ruleId);
        }
    }

    /**
     * 获取缓存的规则详情
     */
    @Override
    public DiscountRuleVo getCachedRuleDetail(Long ruleId) {
        if (ObjectUtil.isNull(ruleId)) {
            return null;
        }
        String key = RULE_DETAIL_KEY + ruleId;
        DiscountRuleVo rule = RedisUtils.getCacheObject(key);
        if (ObjectUtil.isNotNull(rule)) {
            log.debug("从缓存获取规则{}详情", ruleId);
        }
        return rule;
    }

    /**
     * 清除规则详情缓存
     */
    @Override
    public void clearRuleDetailCache(Long ruleId) {
        if (ObjectUtil.isNotNull(ruleId)) {
            String key = RULE_DETAIL_KEY + ruleId;
            RedisUtils.deleteObject(key);
            log.debug("清除规则{}详情缓存", ruleId);
        }
    }

}
