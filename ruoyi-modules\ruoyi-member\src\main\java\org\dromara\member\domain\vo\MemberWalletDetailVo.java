package org.dromara.member.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.member.domain.MemberWalletDetail;

import java.io.Serial;
import java.io.Serializable;



/**
 * 钱包流水记录视图对象 member_wallet_detail
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MemberWalletDetail.class)
public class MemberWalletDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 会员ID
     */
    @ExcelProperty(value = "会员ID")
    private Long memberId;

    /**
     * 会员名称
     */
    @ExcelProperty(value = "会员名称")
    private String memberName;

    /**
     * 关联订单号
     */
    @ExcelProperty(value = "关联订单号")
    private String orderNo;

    /**
     * 业务类型：1-充值，2-消费，3-退款，4-转账，5-活动赠送，6-管理员调整
     */
    @ExcelProperty(value = "业务类型：1-充值，2-消费，3-退款，4-转账，5-活动赠送，6-管理员调整")
    private String businessType;

    /**
     * 金额类型：1-充值金额，2-赠送金额
     */
    @ExcelProperty(value = "金额类型：1-充值金额，2-赠送金额")
    private String amountType;

    /**
     * 变动金额（正数为增加，负数为减少）
     */
    @ExcelProperty(value = "变动金额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "正=数为增加，负数为减少")
    private Long changeAmount;

    /**
     * 变动前余额
     */
    @ExcelProperty(value = "变动前余额")
    private Long balanceBefore;

    /**
     * 变动后余额
     */
    @ExcelProperty(value = "变动后余额")
    private Long balanceAfter;

    /**
     * 变动前充值余额
     */
    @ExcelProperty(value = "变动前充值余额")
    private Long rechargeBalanceBefore;

    /**
     * 变动后充值余额
     */
    @ExcelProperty(value = "变动后充值余额")
    private Long rechargeBalanceAfter;

    /**
     * 变动前赠送余额
     */
    @ExcelProperty(value = "变动前赠送余额")
    private Long bonusBalanceBefore;

    /**
     * 变动后赠送余额
     */
    @ExcelProperty(value = "变动后赠送余额")
    private Long bonusBalanceAfter;

    /**
     * 关联活动ID
     */
    @ExcelProperty(value = "关联活动ID")
    private Long activityId;

    /**
     * 备注说明
     */
    @ExcelProperty(value = "备注说明")
    private String remark;


}
